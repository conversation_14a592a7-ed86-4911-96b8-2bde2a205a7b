# Amazon产品详情采集系统部署检查清单

## 📋 部署前检查

### 1. 文件完整性检查
- [ ] `background.js` - 包含所有采集逻辑
- [ ] `content.js` - 包含页面数据提取和消息处理
- [ ] `popup.html` - 包含详情采集控制按钮
- [ ] `popup.js` - 包含按钮事件处理
- [ ] `manifest.json` - 包含必要权限和资源配置
- [ ] `test_detail_crawler.html` - 测试页面
- [ ] `README_DETAIL_CRAWLER.md` - 使用说明文档

### 2. 配置检查
- [ ] `BASE_URL` 指向正确的后台API服务器
- [ ] API接口路径配置正确：
  - [ ] `/detailTask/waitGets`
  - [ ] `/detailTask/submitResult`
  - [ ] `/spu/submit`
  - [ ] `/sku/submit`
- [ ] 扩展权限配置完整（storage, activeTab, tabs, cookies）
- [ ] Amazon域名权限配置（amazon.com, *.amazon.com）

### 3. 后台API检查
- [ ] 后台服务正常运行
- [ ] 数据库连接正常
- [ ] API接口响应正常
- [ ] 数据表结构匹配（AmazonProductSpuEntity, AmazonProductSkuEntity）

## 🧪 功能测试

### 1. 基础功能测试
- [ ] 扩展安装成功
- [ ] Popup界面正常显示
- [ ] 控制按钮响应正常
- [ ] 测试页面可以打开

### 2. 单变体产品测试
**测试URL**: `https://www.amazon.com/dp/B08N5WRWNW`
- [ ] 能正确提取SPU数据（标题、品牌、评分等）
- [ ] 能提取价格和库存信息
- [ ] 能创建单个SKU记录
- [ ] 数据上传到后台成功

### 3. 多变体产品测试
**测试URL**: `https://www.amazon.com/dp/B0DPPT7HYV`
- [ ] 能检测到多变体产品
- [ ] 能提取所有变体ASIN
- [ ] 能解析变体属性（颜色、尺寸等）
- [ ] 能获取每个SKU的详细信息
- [ ] 批量上传SKU数据成功

### 4. 错误处理测试
- [ ] 无效URL处理正常
- [ ] 网络错误重试机制工作
- [ ] API错误响应处理正确
- [ ] 页面解析失败处理正常

## 🔧 性能测试

### 1. 采集速度测试
- [ ] 单个产品采集时间 < 30秒
- [ ] 多变体产品采集时间 < 2分钟
- [ ] 任务循环间隔正常（10-15秒）

### 2. 内存使用测试
- [ ] 长时间运行无内存泄漏
- [ ] iframe正确清理
- [ ] 本地存储大小合理

### 3. 并发测试
- [ ] 多个标签页同时使用正常
- [ ] 任务队列处理正确
- [ ] 无重复采集问题

## 🛡️ 安全检查

### 1. 权限检查
- [ ] 只请求必要的权限
- [ ] 不访问敏感用户数据
- [ ] API通信使用HTTPS

### 2. 数据安全
- [ ] 敏感信息不存储在本地
- [ ] API密钥安全配置
- [ ] 用户数据加密传输

## 📊 监控设置

### 1. 日志监控
- [ ] 关键操作有日志记录
- [ ] 错误信息详细记录
- [ ] 性能指标监控

### 2. 告警设置
- [ ] API调用失败告警
- [ ] 采集成功率监控
- [ ] 系统资源使用监控

## 🚀 部署步骤

### 1. 开发环境测试
```bash
# 1. 加载扩展到Chrome
# 2. 在Amazon页面测试基本功能
# 3. 检查控制台日志
# 4. 验证数据上传
```

### 2. 预生产环境测试
```bash
# 1. 配置预生产API地址
# 2. 执行完整功能测试
# 3. 性能压力测试
# 4. 数据一致性验证
```

### 3. 生产环境部署
```bash
# 1. 更新生产API配置
# 2. 打包扩展文件
# 3. 分发给用户
# 4. 监控系统状态
```

## ✅ 验收标准

### 1. 功能完整性
- [x] SPU数据提取完整准确
- [x] SKU数据包含价格、库存、变体属性
- [x] 多变体产品正确处理
- [x] 任务管理系统正常工作

### 2. 性能要求
- [x] 单产品采集时间 < 30秒
- [x] 系统稳定运行 > 24小时
- [x] 内存使用 < 100MB
- [x] CPU使用率 < 10%

### 3. 用户体验
- [x] 界面操作简单直观
- [x] 错误提示清晰明确
- [x] 采集进度可视化
- [x] 测试工具易于使用

## 🐛 已知问题

### 1. 限制和约束
- iframe跨域限制可能影响某些SKU数据获取
- Amazon反爬机制可能导致请求被拒绝
- 网络不稳定时可能出现超时

### 2. 解决方案
- 使用模拟数据作为后备方案
- 添加随机延迟和重试机制
- 优化错误处理和用户提示

## 📞 支持联系

### 技术支持
- 开发团队：[联系方式]
- 文档地址：README_DETAIL_CRAWLER.md
- 问题反馈：[反馈渠道]

### 紧急联系
- 系统故障：[紧急联系方式]
- 数据问题：[数据团队联系方式]

---

**部署完成后请确保：**
1. 所有测试项目都已通过 ✅
2. 监控系统正常运行 📊
3. 用户培训已完成 👥
4. 应急预案已准备 🚨
