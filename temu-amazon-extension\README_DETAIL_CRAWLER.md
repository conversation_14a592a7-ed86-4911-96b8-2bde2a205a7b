# Amazon产品详情采集系统使用说明

## 🎯 功能概述

Amazon产品详情采集系统是一个强大的Chrome扩展功能，能够自动采集Amazon产品的详细信息，包括：

- **SPU数据**：产品标题、品牌、评分、图片、描述、技术规格等
- **SKU数据**：价格、库存、变体属性（颜色、尺寸等）
- **多变体支持**：自动检测并采集所有产品变体
- **智能解析**：从JavaScript数据中提取变体信息

## 🚀 快速开始

### 1. 安装扩展
1. 将扩展文件夹加载到Chrome浏览器
2. 确保扩展已启用并有必要的权限

### 2. 配置后台API
确保后台API服务正在运行，包括以下接口：
- `GET /api/amazon/page/task/waitGets` - 获取待处理任务
- `PUT /api/amazon/page/task/submitResult` - 提交任务结果
- `POST /api/amazon/page/task/submitSpuSku` - 一次性提交完整产品数据（SPU + SKU）

## 🔌 API接口详细说明

### 1. 获取待处理任务
**接口**: `GET /api/amazon/page/task/waitGets`

**请求参数**:
```
Query参数:
- clientId: string (必需) - 客户端唯一标识符
```

**请求示例**:
```
GET /api/amazon/page/task/waitGets?clientId=client_12345_v1.0.0
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 12345,
    "entryAsin": "B0DPPT7HYV",
    "url": "https://www.amazon.com/dp/B0DPPT7HYV",
    "status": 1,
    "retryCount": 0,
    "listPageTitle": "LEGO Creator 3-in-1 Deep Sea Creatures",
    "listPagePrice": 29.99,
    "listOrginPrice": 39.99,
    "listBoughtNum": 1000,
    "listPageRating": 4.5,
    "listPageReviewCount": 1234,
    "listPageMainImageUrl": "https://m.media-amazon.com/images/I/81abc123.jpg",
    "listPagePrimeInfo": "Prime",
    "isSponsored": false,
    "amazonCategoryId": 12345,
    "listTaskId": 100,
    "errorMessage": null,
    "createdAt": "2024-12-15T10:00:00.000Z",
    "updatedAt": "2024-12-15T10:00:00.000Z"
  }
}
```

**响应说明**:
- `success`: boolean - 请求是否成功
- `data`: object - 任务数据（无任务时为null）
- `id`: number - 任务唯一ID
- `entryAsin`: string - 产品ASIN（用作SPU主键）
- `url`: string - Amazon产品详情页URL
- `status`: number - 任务状态（0-等待处理，1-处理中，2-处理完成，3-处理失败）
- `listPageTitle`: string - 列表页产品标题
- `listPagePrice`: number - 列表页价格
- `listPageRating`: number - 列表页评分
- `listPageReviewCount`: number - 列表页评价数量
- 其他字段为列表页采集的基础信息

### 2. 提交任务结果
**接口**: `PUT /api/amazon/page/task/submitResult`

**请求体格式**:
```json
{
  "taskId": 12345,
  "status": "completed",
  "errorMessage": "",
  "completedAt": "2024-12-15T10:30:00.000Z"
}
```

**参数说明**:
- `taskId`: number (必需) - 任务ID
- `status`: string (必需) - 任务状态 ("completed" | "failed")
- `errorMessage`: string (可选) - 错误信息（失败时提供）
- `completedAt`: string (必需) - 完成时间（ISO格式）

### 3. 提交产品详情数据
**接口**: `POST /api/amazon/page/task/submitSpuSku`

**请求体格式**:
```json
{
  "spu": {
    "asin": "B0DPPT7HYV",
    "title": "LEGO Creator 3-in-1 Deep Sea Creatures",
    "brand": "LEGO",
    "rating": 4.5,
    "reviewCount": 1234,
    "mainImageUrl": "https://m.media-amazon.com/images/I/81abc123.jpg",
    "imageUrls": "[\"https://m.media-amazon.com/images/I/81abc123.jpg\",\"https://m.media-amazon.com/images/I/81def456.jpg\"]",
    "bulletPoints": "[\"High-quality LEGO bricks\",\"3-in-1 building experience\",\"Ages 7+\"]",
    "description": "Build and rebuild 3 different sea creatures with this LEGO Creator set...",
    "productDetails": "{\"Brand\":\"LEGO\",\"Age Range\":\"7 years and up\",\"Material\":\"Plastic\"}",
    "productAttributes": "{\"Dimensions\":\"10 x 8 x 6 inches\",\"Weight\":\"1.2 pounds\",\"Manufacturer\":\"LEGO\"}",
    "categoryPath": "Toys & Games > Building Toys > Building Sets",
    "temuProductDetail": "{\"basic_info\":{\"title\":\"LEGO Creator 3-in-1 Deep Sea Creatures\",\"brand\":\"LEGO\",\"rating\":4.5,\"review_count\":1234},\"media\":{\"main_image\":\"https://m.media-amazon.com/images/I/81abc123.jpg\",\"images\":[\"https://m.media-amazon.com/images/I/81abc123.jpg\"]},\"description\":{\"bullet_points\":[\"High-quality LEGO bricks\"],\"long_description\":\"Build and rebuild 3 different sea creatures...\"},\"specifications\":{\"Brand\":\"LEGO\",\"Age Range\":\"7 years and up\"},\"category\":{\"path\":\"Toys & Games > Building Toys > Building Sets\"}}"
  },
  "skuList": [
    {
      "asin": "B0DLNVJM3R",
      "parentAsin": "B0DPPT7HYV",
      "price": 9.99,
      "currency": "USD",
      "stockStatus": "In Stock",
      "imageUrl": "https://m.media-amazon.com/images/I/81blue123.jpg",
      "variationAttributes": "{\"Color\":\"Blue Bead Spinner Kit\"}"
    },
    {
      "asin": "B0DLNSW59D",
      "parentAsin": "B0DPPT7HYV",
      "price": 10.99,
      "currency": "USD",
      "stockStatus": "In Stock",
      "imageUrl": "https://m.media-amazon.com/images/I/81pink123.jpg",
      "variationAttributes": "{\"Color\":\"Light Pink Bead Spinner\"}"
    }
  ],
  "timestamp": "2024-12-15T10:30:00.000Z",
  "totalSkuCount": 2
}
```

**参数说明**:

#### SPU字段说明:
- `asin`: string (必需) - Amazon标准识别号，SPU主键
- `title`: string (可选) - 产品标题
- `brand`: string (可选) - 品牌名称
- `rating`: number (可选) - 评分（0-5）
- `reviewCount`: number (可选) - 评价数量
- `mainImageUrl`: string (可选) - 主图URL
- `imageUrls`: string (可选) - 图片URL数组的JSON字符串
- `bulletPoints`: string (可选) - 五点描述数组的JSON字符串
- `description`: string (可选) - 产品描述（最多2000字符）
- `productDetails`: string (可选) - 产品详情的JSON字符串
- `productAttributes`: string (可选) - 技术规格的JSON字符串
- `categoryPath`: string (可选) - 类目路径
- `temuProductDetail`: string (可选) - Temu格式产品详情的JSON字符串

#### SKU字段说明:
- `asin`: string (必需) - SKU的Amazon标准识别号
- `parentAsin`: string (必需) - 父产品ASIN（SPU的ASIN）
- `price`: number (可选) - 价格
- `currency`: string (可选) - 货币代码（默认USD）
- `stockStatus`: string (可选) - 库存状态
- `imageUrl`: string (可选) - 变体图片URL
- `variationAttributes`: string (可选) - 变体属性的JSON字符串

#### 其他字段:
- `timestamp`: string (必需) - 提交时间戳（ISO格式）
- `totalSkuCount`: number (必需) - SKU总数量

**响应格式**:
```json
{
  "success": true,
  "message": "产品详情数据提交成功",
  "data": {
    "spuId": "B0DPPT7HYV",
    "skuCount": 2,
    "processedAt": "2024-12-15T10:30:00.000Z"
  }
}
```

### 3. 使用方式

#### 方式一：自动采集循环
1. 点击扩展图标打开popup界面
2. 点击"启动详情采集"按钮
3. 系统将自动从后台获取待处理任务并进行采集
4. 每个任务完成后会随机等待10-15秒再处理下一个
5. 可以勾选"扩展启动时自动开始详情采集"实现完全自动化

#### 方式零：完全自动化（推荐）
1. 在popup界面勾选"扩展启动时自动开始详情采集"
2. 系统将在Chrome启动时自动开始采集
3. 无需人工干预，background.js独立运行
4. 可通过popup界面查看实时进度和控制

#### 方式二：测试单个产品
1. 在Amazon产品详情页面（如：https://www.amazon.com/dp/B0DPPT7HYV）
2. 点击扩展图标，选择"测试当前产品"
3. 系统将立即采集当前页面的产品信息

#### 方式三：使用测试页面
1. 点击"测试页面"按钮打开专用测试界面
2. 输入Amazon产品URL进行测试
3. 查看详细的采集结果和系统状态

## 📊 数据结构

### SPU数据结构
```json
{
  "asin": "B0DPPT7HYV",
  "title": "产品标题",
  "brand": "品牌名称",
  "rating": 4.5,
  "reviewCount": 1234,
  "mainImageUrl": "主图URL",
  "imageUrls": "[图片URL数组]",
  "bulletPoints": "[五点描述数组]",
  "description": "产品描述",
  "productDetails": "{产品详情JSON}",
  "productAttributes": "{技术规格JSON}",
  "categoryPath": "类目路径",
  "temuProductDetail": "{Temu格式产品详情}"
}
```

### SKU数据结构
```json
{
  "asin": "B0DLNVJM3R",
  "parentAsin": "B0DPPT7HYV",
  "price": 9.99,
  "currency": "USD",
  "stockStatus": "In Stock",
  "imageUrl": "变体图片URL",
  "variationAttributes": "{\"Color\":\"Blue\",\"Size\":\"Large\"}"
}
```

## 🔧 技术特性

### 多变体处理
- 自动检测`dimensionToAsinMap`和`variationValues`
- 支持多维度变体（颜色+尺寸）
- 智能解析变体属性和图片

### 数据提取算法
- 从JavaScript数据中提取变体信息
- 多种CSS选择器确保数据完整性
- 智能清理HTML内容，去除无关代码

### 错误处理
- 完善的异常捕获和日志记录
- 自动重试机制
- 任务状态跟踪（pending->processing->completed）

## 📝 使用示例

### 示例1：多变体产品
产品URL: `https://www.amazon.com/dp/B0DPPT7HYV`

**检测结果：**
- 父ASIN: B0DPPT7HYV
- 变体数量: 4个
- 变体属性: 颜色（Blue, Light Pink, Peach Pink, Pink）

**采集结果：**
- 1个SPU记录
- 4个SKU记录，每个包含不同的颜色和价格

### 示例2：单变体产品
产品URL: `https://www.amazon.com/dp/B08N5WRWNW`

**检测结果：**
- 单一ASIN: B08N5WRWNW
- 无变体数据

**采集结果：**
- 1个SPU记录
- 1个SKU记录

## ⚙️ 配置选项

### 后台API配置
在`background.js`中修改以下URL：
```javascript
var DETAIL_TASK_WAITGET_URL = BASE_URL + "/api/amazon/page/task/waitGets";
var DETAIL_TASK_SUBMIT_URL = BASE_URL + "/api/amazon/page/task/submitResult";
var PRODUCT_DETAIL_SUBMIT_URL = BASE_URL + "/api/amazon/page/task/submitSpuSku";
```

### 数据提交格式总结
完整的产品详情数据提交格式（详细参数见上方API接口说明）：
```json
{
  "spu": {
    "asin": "B0DPPT7HYV",
    "title": "产品标题",
    "brand": "品牌名称",
    "rating": 4.5,
    "reviewCount": 1234,
    "mainImageUrl": "主图URL",
    "imageUrls": "[图片URL数组JSON]",
    "bulletPoints": "[五点描述JSON]",
    "description": "产品描述",
    "productDetails": "{产品详情JSON}",
    "productAttributes": "{技术规格JSON}",
    "categoryPath": "类目路径",
    "temuProductDetail": "{Temu格式详情JSON}"
  },
  "skuList": [
    {
      "asin": "B0DLNVJM3R",
      "parentAsin": "B0DPPT7HYV",
      "price": 9.99,
      "currency": "USD",
      "stockStatus": "In Stock",
      "imageUrl": "变体图片URL",
      "variationAttributes": "{变体属性JSON}"
    }
  ],
  "timestamp": "2024-12-15T10:30:00.000Z",
  "totalSkuCount": 1
}
```

### 采集参数调整
- 任务间隔时间：10-15秒（可在`startAmazonDetailCrawlingLoop`函数中修改）
- SKU请求延迟：1-3秒（可在`fetchIndividualSkuDetails`函数中修改）
- 超时设置：15秒（可在`fetchSkuDetailsFromUrl`函数中修改）

## 🐛 故障排除

### 常见问题

**1. 无法获取页面数据**
- 确保在Amazon产品详情页面运行
- 检查扩展权限是否正确配置
- 查看控制台是否有JavaScript错误

**2. 变体数据提取失败**
- 检查页面是否包含`dimensionToAsinMap`数据
- 确认产品确实有多个变体
- 查看控制台日志了解具体错误

**3. API提交失败**
- 检查后台API服务是否正常运行
- 确认API接口URL配置正确
- 查看网络请求是否成功

### 调试方法

**1. 开启控制台日志**
```javascript
// 在background.js中查看详细日志
console.log("开始处理Amazon产品详情:", productUrl);
```

**2. 使用测试页面**
- 打开测试页面进行单独测试
- 查看详细的采集结果
- 检查系统状态信息

**3. 检查本地存储**
```javascript
// 在控制台中查看存储的任务信息
chrome.storage.local.get(null, console.log);
```

## 📈 性能优化

### 采集效率
- 使用iframe异步获取SKU详情
- 批量提交SKU数据减少API调用
- 智能缓存避免重复请求

### 内存管理
- 及时清理临时DOM元素
- 限制图片数量和描述长度
- 定期清理本地存储

### 网络优化
- 随机延迟避免被反爬
- 错误重试机制
- 超时控制防止卡死

## 🔄 更新日志

### v1.0.0 (2024-12-15)
- ✅ 实现基础SPU数据提取
- ✅ 支持多变体产品采集
- ✅ 添加任务管理系统
- ✅ 集成popup控制界面
- ✅ 创建测试页面工具

## � 后台开发API总结

### 需要实现的接口

#### 1. 获取待处理任务
```
GET /api/amazon/page/task/waitGets?clientId={clientId}
```
**功能**: 返回一个待处理的Amazon产品详情采集任务
**返回**: 任务对象（包含id、entryAsin、url、listPageTitle等）或null

#### 2. 更新任务状态
```
PUT /api/amazon/page/task/submitResult
Content-Type: application/json

{
  "taskId": 12345,
  "status": "completed|failed",
  "errorMessage": "错误信息（可选）",
  "completedAt": "2024-12-15T10:30:00.000Z"
}
```
**功能**: 标记任务完成或失败

#### 3. 保存产品详情数据
```
POST /api/amazon/page/task/submitSpuSku
Content-Type: application/json

{
  "spu": { /* SPU数据对象 */ },
  "skuList": [ /* SKU数据数组 */ ],
  "timestamp": "2024-12-15T10:30:00.000Z",
  "totalSkuCount": 2
}
```
**功能**: 一次性保存完整的产品详情数据（SPU + 所有SKU）

### 数据库表结构参考
- **AmazonProductSpuEntity**: 存储SPU数据
- **AmazonProductSkuEntity**: 存储SKU数据
- 详细字段说明见上方API接口文档

### 关键特性
- ✅ 一次性提交减少API调用
- ✅ 完整的错误处理和状态跟踪
- ✅ 支持多变体产品的复杂数据结构
- ✅ JSON格式存储复杂字段（图片、属性等）

## �📞 技术支持

如遇到问题，请提供以下信息：
1. 产品URL
2. 控制台错误日志
3. 扩展版本号
4. 浏览器版本

---

**注意：** 请遵守Amazon的使用条款，合理使用采集功能，避免对服务器造成过大压力。
