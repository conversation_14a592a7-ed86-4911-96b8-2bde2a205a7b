/**
 * Amazon页面解析器模块
 * 专门负责从Amazon页面提取SPU和SKU数据
 */

// 获取配置（假设config.js已经加载）
const amazonParserConfig = window.AmazonCrawlerConfig || {};

/**
 * Amazon页面解析器类
 */
class AmazonParser {
  constructor() {
    this.config = amazonParserConfig.AMAZON_CONFIG || {};
    this.systemConfig = amazonParserConfig.SYSTEM_CONFIG || {};
    this.errorConfig = amazonParserConfig.ERROR_CONFIG || {};
  }

  /**
   * 从Amazon页面提取SPU数据
   * @param {Document} doc 页面文档对象
   * @param {string} pageUrl 页面URL
   * @param {Object} taskData 任务数据（可选）
   * @returns {Object} SPU数据对象
   */
  extractSpuData(doc, pageUrl = '', taskData = null) {
    console.log("开始提取SPU数据...");
    
    const spuData = {};
    
    // 1. 提取ASIN（优先级：任务entryAsin > parentAsin > data-asin > URL）
    spuData.asin = this._extractAsin(doc, pageUrl, taskData);
    
    // 2. 提取基础信息
    spuData.title = this._extractTitle(doc);
    spuData.brand = this._extractBrand(doc);
    spuData.rating = this._extractRating(doc);
    spuData.reviewCount = this._extractReviewCount(doc);
    
    // 3. 提取图片信息
    spuData.mainImageUrl = this._extractMainImage(doc);
    spuData.imageUrls = this._extractImageUrls(doc);
    
    // 4. 提取描述信息
    spuData.bulletPoints = this._extractBulletPoints(doc);
    spuData.description = this._extractDescription(doc);
    
    // 5. 提取产品详情和规格
    const { productDetails, productAttributes } = this._extractProductDetails(doc);
    spuData.productDetails = JSON.stringify(productDetails);
    spuData.productAttributes = JSON.stringify(productAttributes);
    
    // 6. 提取类目路径
    spuData.categoryPath = this._extractCategoryPath(doc);
    
    // 7. 组装Temu格式的产品详情
    spuData.temuProductDetail = this._buildTemuProductDetail(spuData, productAttributes);
    
    console.log("SPU数据提取完成:", spuData.asin, "-", spuData.title?.substring(0, 50) + "...");
    return spuData;
  }

  /**
   * 从JavaScript数据中提取变体信息
   * @param {Document} doc 页面文档对象
   * @returns {Object} 变体数据对象
   */
  extractVariationData(doc) {
    console.log("开始提取变体数据...");
    
    const variationData = {};
    const scripts = doc.querySelectorAll('script');
    
    for (const script of scripts) {
      const scriptContent = script.textContent;
      if (!scriptContent || !scriptContent.includes('dimensionToAsinMap')) {
        continue;
      }
      
      try {
        // 使用配置中的正则表达式提取各种变体数据
        const patterns = this.config.JS_DATA_PATTERNS;
        
        // 提取dimensionToAsinMap
        const dimensionMatch = scriptContent.match(patterns.DIMENSION_TO_ASIN);
        if (dimensionMatch) {
          variationData.dimensionToAsinMap = JSON.parse(dimensionMatch[1]);
          console.log("找到dimensionToAsinMap:", variationData.dimensionToAsinMap);
        }
        
        // 提取variationValues
        const variationMatch = scriptContent.match(patterns.VARIATION_VALUES);
        if (variationMatch) {
          variationData.variationValues = JSON.parse(variationMatch[1]);
          console.log("找到variationValues:", variationData.variationValues);
        }
        
        // 提取colorToAsin
        const colorToAsinMatch = scriptContent.match(patterns.COLOR_TO_ASIN);
        if (colorToAsinMatch) {
          variationData.colorToAsin = JSON.parse(colorToAsinMatch[1]);
          console.log("找到colorToAsin:", variationData.colorToAsin);
        }
        
        // 提取colorImages
        const colorImagesMatch = scriptContent.match(patterns.COLOR_IMAGES);
        if (colorImagesMatch) {
          try {
            variationData.colorImages = JSON.parse(colorImagesMatch[1]);
            console.log("找到colorImages，变体数量:", Object.keys(variationData.colorImages).length);
          } catch (e) {
            console.warn("解析colorImages失败:", e);
          }
        }
        
        // 提取parentAsin
        const parentMatch = scriptContent.match(patterns.PARENT_ASIN);
        if (parentMatch) {
          variationData.parentAsin = parentMatch[1];
          console.log("找到parentAsin:", parentMatch[1]);
        }
        
        break; // 找到数据后退出循环
      } catch (error) {
        console.warn("解析变体数据时出错:", error);
        continue;
      }
    }
    
    return variationData;
  }

  /**
   * 判断是否为多变体产品
   * @param {Object} variationData 变体数据
   * @returns {boolean} 是否为多变体
   */
  isMultiVariantProduct(variationData) {
    const hasMultipleDimensions = variationData.dimensionToAsinMap && 
                                  Object.keys(variationData.dimensionToAsinMap).length > 1;
    const hasMultipleColors = variationData.colorToAsin && 
                             Object.keys(variationData.colorToAsin).length > 1;
    
    return hasMultipleDimensions || hasMultipleColors;
  }

  /**
   * 从变体数据中提取所有SKU信息
   * @param {Object} variationData 变体数据
   * @param {string} parentAsin 父ASIN
   * @returns {Array} SKU数据数组
   */
  extractSkuDataFromVariations(variationData, parentAsin) {
    console.log("开始从变体数据提取SKU信息...");
    
    const skuDataList = [];
    
    if (!variationData.dimensionToAsinMap) {
      console.log("未找到变体数据，将创建单个SKU");
      return [];
    }
    
    const dimensionMap = variationData.dimensionToAsinMap;
    const variationValues = variationData.variationValues || {};
    const colorToAsin = variationData.colorToAsin || {};
    const colorImages = variationData.colorImages || {};
    
    // 获取变体属性名称列表
    const sizeNames = variationValues.size_name || [];
    const colorNames = variationValues.color_name || [];
    
    for (const [index, asin] of Object.entries(dimensionMap)) {
      const skuData = {
        asin: asin,
        parentAsin: parentAsin,
        currency: 'USD',
        stockStatus: 'In Stock',
        price: null,
        imageUrl: null,
        variationAttributes: {}
      };
      
      // 解析变体属性
      const variationAttributes = this._parseVariationAttributes(
        index, sizeNames, colorNames, colorToAsin, asin
      );
      
      // 获取变体图片
      if (variationAttributes.Color && colorImages[variationAttributes.Color]) {
        const images = colorImages[variationAttributes.Color];
        if (images && images.length > 0 && images[0].large) {
          skuData.imageUrl = images[0].large;
        }
      }
      
      skuData.variationAttributes = JSON.stringify(variationAttributes);
      
      const displayName = variationAttributes.Color || `Variant ${index}`;
      console.log(`SKU ${asin}: ${displayName}`);
      skuDataList.push(skuData);
    }
    
    console.log(`提取到 ${skuDataList.length} 个SKU变体`);
    return skuDataList;
  }

  /**
   * 为单变体产品创建SKU数据
   * @param {Document} doc 页面文档对象
   * @param {string} parentAsin 父ASIN
   * @returns {Array} 包含单个SKU的数组
   */
  createSingleSkuFromPage(doc, parentAsin) {
    console.log("创建单变体SKU数据...");
    
    const skuData = {
      asin: parentAsin,
      parentAsin: parentAsin,
      currency: 'USD',
      stockStatus: 'In Stock',
      price: null,
      imageUrl: null,
      variationAttributes: JSON.stringify({})
    };
    
    // 提取价格
    skuData.price = this._extractPrice(doc);
    
    // 提取主图
    skuData.imageUrl = this._extractMainImage(doc);
    
    // 提取库存状态
    skuData.stockStatus = this._extractStockStatus(doc);
    
    console.log("单变体SKU创建完成");
    return [skuData];
  }

  // ==================== 私有方法 ====================

  /**
   * 提取ASIN
   * @private
   */
  _extractAsin(doc, pageUrl, taskData) {
    // 优先使用任务中的entryAsin
    if (taskData && taskData.entryAsin) {
      console.log("使用任务entryAsin作为SPU主键:", taskData.entryAsin);
      return taskData.entryAsin;
    }
    
    // 尝试从JavaScript中提取parentAsin
    const scripts = doc.querySelectorAll('script');
    for (const script of scripts) {
      if (script.textContent && script.textContent.includes('parentAsin')) {
        const parentMatch = script.textContent.match(this.config.JS_DATA_PATTERNS.PARENT_ASIN);
        if (parentMatch) {
          console.log("使用parentAsin作为SPU主键:", parentMatch[1]);
          return parentMatch[1];
        }
      }
    }
    
    // 从页面元素中提取
    const asinElement = doc.querySelector('[data-asin]');
    if (asinElement) {
      const asin = asinElement.getAttribute('data-asin');
      console.log("使用data-asin作为SPU主键:", asin);
      return asin;
    }
    
    // 从URL中提取ASIN
    const urlMatch = pageUrl.match(this.config.URL_PATTERNS.PRODUCT_DETAIL);
    const asin = urlMatch ? urlMatch[1] : 'UNKNOWN';
    console.log("使用URL中的ASIN作为SPU主键:", asin);
    return asin;
  }

  /**
   * 提取产品标题
   * @private
   */
  _extractTitle(doc) {
    const titleElement = doc.querySelector(this.config.SELECTORS.PRODUCT_TITLE);
    return titleElement ? titleElement.textContent.trim() : null;
  }

  /**
   * 提取品牌信息
   * @private
   */
  _extractBrand(doc) {
    const brandElement = doc.querySelector(this.config.SELECTORS.BRAND_INFO);
    if (brandElement) {
      const brandText = brandElement.textContent.trim();
      const brandMatch = brandText.match(/Visit the (.+?) Store/);
      return brandMatch ? brandMatch[1] : brandText;
    }
    return null;
  }

  /**
   * 提取评分
   * @private
   */
  _extractRating(doc) {
    const ratingElement = doc.querySelector(this.config.SELECTORS.RATING);
    if (ratingElement) {
      const ratingText = ratingElement.textContent.trim();
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
      return ratingMatch ? parseFloat(ratingMatch[1]) : null;
    }
    return null;
  }

  /**
   * 提取评价数量
   * @private
   */
  _extractReviewCount(doc) {
    const reviewElement = doc.querySelector(this.config.SELECTORS.REVIEW_COUNT);
    if (reviewElement) {
      const reviewText = reviewElement.textContent.trim();
      const reviewMatch = reviewText.replace(/,/g, '').match(/(\d+)/);
      return reviewMatch ? parseInt(reviewMatch[1]) : null;
    }
    return null;
  }

  /**
   * 提取主图URL
   * @private
   */
  _extractMainImage(doc) {
    const mainImageElement = doc.querySelector(this.config.SELECTORS.MAIN_IMAGE);
    if (mainImageElement) {
      return mainImageElement.src || mainImageElement.getAttribute('data-src');
    }
    return null;
  }

  /**
   * 提取所有图片URLs
   * @private
   */
  _extractImageUrls(doc) {
    const imageUrls = [];
    const imageElements = doc.querySelectorAll(this.config.SELECTORS.ALT_IMAGES);

    for (const img of imageElements) {
      const imgUrl = img.src || img.getAttribute('data-src');
      if (imgUrl && imgUrl.includes('amazon.com')) {
        imageUrls.push(imgUrl);
      }
    }

    const maxImages = this.systemConfig.CRAWLING.MAX_IMAGES;
    return JSON.stringify(imageUrls.slice(0, maxImages));
  }

  /**
   * 提取五点描述
   * @private
   */
  _extractBulletPoints(doc) {
    const bulletPoints = [];
    const bulletElements = doc.querySelectorAll(this.config.SELECTORS.BULLET_POINTS);

    for (const bullet of bulletElements) {
      const text = bullet.textContent.trim();
      if (text && text.length > 10) { // 过滤掉太短的文本
        bulletPoints.push(text);
      }
    }

    const maxBullets = this.systemConfig.CRAWLING.MAX_BULLET_POINTS;
    return JSON.stringify(bulletPoints.slice(0, maxBullets));
  }

  /**
   * 提取商品描述
   * @private
   */
  _extractDescription(doc) {
    const descElement = doc.querySelector(this.config.SELECTORS.DESCRIPTION);
    if (descElement) {
      const maxLength = this.systemConfig.CRAWLING.MAX_DESCRIPTION_LENGTH;
      return descElement.textContent.trim().substring(0, maxLength);
    }
    return null;
  }

  /**
   * 提取产品详情和技术规格
   * @private
   */
  _extractProductDetails(doc) {
    const productDetails = {};
    const productAttributes = {};

    // 从产品详情表格中提取
    for (const selector of this.config.SELECTORS.DETAIL_TABLES) {
      const detailElements = doc.querySelectorAll(selector);

      for (const row of detailElements) {
        const { key, value } = this._extractTableRowData(row);

        if (key && value && key !== 'Customer Reviews') {
          const cleanKey = key.replace(':', '').trim();
          productDetails[cleanKey] = value;
          productAttributes[cleanKey] = value;
        }
      }
    }

    return { productDetails, productAttributes };
  }

  /**
   * 提取表格行数据
   * @private
   */
  _extractTableRowData(row) {
    const cells = row.querySelectorAll('td');
    let key, value;

    if (cells.length >= 2) {
      key = cells[0].textContent.trim();
      value = cells[1].textContent.trim();
    } else {
      const thElement = row.querySelector('th');
      const tdElement = row.querySelector('td');
      if (thElement && tdElement) {
        key = thElement.textContent.trim();
        value = tdElement.textContent.trim();
      }
    }

    return { key, value };
  }

  /**
   * 提取类目路径
   * @private
   */
  _extractCategoryPath(doc) {
    const breadcrumbElements = doc.querySelectorAll(this.config.SELECTORS.BREADCRUMBS);
    const categoryPath = [];

    for (const breadcrumb of breadcrumbElements) {
      const text = breadcrumb.textContent.trim();
      if (text && text !== 'Home' && text !== 'Amazon.com') {
        categoryPath.push(text);
      }
    }

    return categoryPath.join(' > ');
  }

  /**
   * 组装Temu格式的产品详情
   * @private
   */
  _buildTemuProductDetail(spuData, productAttributes) {
    const temuProductDetail = {
      basic_info: {
        title: spuData.title || '',
        brand: spuData.brand || '',
        rating: spuData.rating,
        review_count: spuData.reviewCount
      },
      media: {
        main_image: spuData.mainImageUrl || '',
        images: JSON.parse(spuData.imageUrls || '[]')
      },
      description: {
        bullet_points: JSON.parse(spuData.bulletPoints || '[]'),
        long_description: spuData.description || ''
      },
      specifications: productAttributes,
      category: {
        path: spuData.categoryPath || ''
      }
    };

    return JSON.stringify(temuProductDetail);
  }

  /**
   * 提取价格
   * @private
   */
  _extractPrice(doc) {
    const priceElement = doc.querySelector(this.config.SELECTORS.PRICE);
    if (priceElement) {
      const priceText = priceElement.textContent.trim();
      const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/);
      if (priceMatch) {
        const price = parseFloat(priceMatch[0].replace(',', ''));
        console.log("价格:", price);
        return price;
      }
    }
    return null;
  }

  /**
   * 提取库存状态
   * @private
   */
  _extractStockStatus(doc) {
    for (const selector of this.config.SELECTORS.AVAILABILITY) {
      const availabilityElement = doc.querySelector(selector);
      if (availabilityElement) {
        const availabilityText = availabilityElement.textContent.trim();
        if (availabilityText && availabilityText.length > 0) {
          const status = this._parseStockStatus(availabilityText);
          if (status) {
            console.log("库存状态:", status);
            return status;
          }
        }
      }
    }
    return 'In Stock'; // 默认状态
  }

  /**
   * 解析库存状态文本
   * @private
   */
  _parseStockStatus(text) {
    if (text.includes('In Stock')) {
      return 'In Stock';
    } else if (text.includes('Out of Stock')) {
      return 'Out of Stock';
    } else if (text.toLowerCase().includes('only') && text.toLowerCase().includes('left')) {
      const quantityMatch = text.toLowerCase().match(/only\s+(\d+)\s+left/);
      if (quantityMatch) {
        return `Only ${quantityMatch[1]} left in stock`;
      } else {
        return text.substring(0, 50);
      }
    } else {
      return text.substring(0, 50);
    }
  }

  /**
   * 解析变体属性
   * @private
   */
  _parseVariationAttributes(index, sizeNames, colorNames, colorToAsin, asin) {
    const variationAttributes = {};
    let displayName = `Variant ${index}`;

    try {
      // 检查是否为多维度变体索引（如"0_0", "1_3"）
      if (index.includes('_')) {
        const parts = index.split('_');
        if (parts.length >= 2) {
          const sizeIndex = parseInt(parts[0]);
          const colorIndex = parseInt(parts[1]);

          if (sizeIndex < sizeNames.length) {
            variationAttributes.Size = sizeNames[sizeIndex];
          }

          if (colorIndex < colorNames.length) {
            variationAttributes.Color = colorNames[colorIndex];
          }

          displayName = `${variationAttributes.Color || 'Unknown'} - ${variationAttributes.Size || 'Unknown'}`;
        }
      } else {
        // 单维度变体：尝试解析为颜色索引
        const colorIndex = parseInt(index);
        if (colorIndex < colorNames.length) {
          variationAttributes.Color = colorNames[colorIndex];
          displayName = colorNames[colorIndex];
        }
      }
    } catch (error) {
      console.warn("解析变体属性失败:", error);
    }

    // 尝试从colorToAsin中获取更准确的颜色信息
    for (const [colorName, colorData] of Object.entries(colorToAsin)) {
      if (colorData.asin === asin) {
        variationAttributes.Color = colorName;
        displayName = colorName;
        break;
      }
    }

    return variationAttributes;
  }
}

// 创建全局实例
if (typeof window !== 'undefined') {
  window.AmazonParser = new AmazonParser();
}
