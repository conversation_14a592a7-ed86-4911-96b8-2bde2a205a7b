//var BASE_URL = "https://51erp.store/xace-web/api/amazon";
 var BASE_URL = "http://127.0.0.1:32000/api/amazon";

var WAITGET_URL = BASE_URL + "/list/task2/waitGets";
var POSTOFFER_URL = BASE_URL + "/collect2/putOfferJson";
var BATCH_URL = BASE_URL + "/collect/batch";
var NEW_BATCH_URL = BASE_URL + "/collect2/newSellerCollect";
var REMOVE_URL = BASE_URL + "/collect/remove";

var CATE_WAITGET_URL = BASE_URL + "/task/waitGets";
var CATE_POSTOFFER_URL = BASE_URL + "/task/putTaskJson";

var uniqueId = "";
let lastUpdateTime = Date.now();


function util_getscript_sellerData_content(html = "") {
  var scriptTagPattern = /<script\b[^>]*>([\s\S]*?)<\/script>/gm;
  var matches = html.match(scriptTagPattern);
  if (matches) {
    for (var i = 0; i < matches.length; i++) {
      var scriptContent = matches[i].replace(scriptTagPattern, "$1");
      if (
        scriptContent.indexOf("forceBuyNow") != -1 &&
        scriptContent.indexOf("offer") != -1 &&
        scriptContent.indexOf("seller") != -1 &&
        scriptContent.indexOf("company") != -1
      ) {
        return scriptContent;
      }
    }
  }
  return false;
}

function util_get_random_number(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function util_getscript_genmaiData_content(html = "") {
  var scriptTagPattern = /<script\b[^>]*>([\s\S]*?)<\/script>/gm;
  var matches = html.match(scriptTagPattern);
  if (matches) {
    for (var i = 0; i < matches.length; i++) {
      var scriptContent = matches[i].replace(scriptTagPattern, "$1");
      if (
        scriptContent.indexOf("ata-analytics-view-custom-cheapest-price") != -1
      ) {
        return scriptContent;
      }
    }
  }
  return false;
}

function util_checkFanpa(str = "") {
  const searchString = "This item is sold out";
  const regex = new RegExp(searchString);
  return regex.test(str);
}

function pause_s_time() {
  chrome.storage.local.set({ pause_s_time: Date.now() });
}

// 获取 HTML 中指定选择器对应元素的文本内容
function extractTextFromHTML(html, selector) {
  const regex = new RegExp(`<${selector}[^>]*>([^<]*)<\/${selector}>`, "g");
  const matches = [];
  let match;
  while ((match = regex.exec(html)) !== null) {
    matches.push(match[1]);
  }
  return matches.length > 0 ? matches.join(" ") : "";
}

async function request_offer() {
  console.log(ct(), "后台请求offer详情");

  chrome.storage.local.get(["uniqueId"], function (result) {
    if (!result.hasOwnProperty("uniqueId")) {
      uniqueId = generateUniqueId();
      chrome.storage.local.set({ uniqueId: uniqueId }, function () {
        console.log(ct(), "Unique ID is set to " + uniqueId);
      });
    } else {
      uniqueId = result.uniqueId;
    }

    // 请求接口获取数据
    fetch(WAITGET_URL, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.code === 200) {
          console.log(ct(), "请求到：waitGets数据。" + data.data.length);
       
          if (data.data.length == 0) {
            //等待1个小时
            console.log(
              ct(),
              "===============后台没有待处理数据，等待15分钟=================="
            );
            setTimeout(pause_s_time, 900000); // 15分钟 = 15 * 60 * 1000 毫秒
          }

          isRequest = true;

          data.data.forEach((item) => {
            console.log(
              ct(),
              ".....加载：" + item.offerLink + ", ID:" + item.id
            );
            let key = "request_offer_detail_" + item.id;
            let obj = {
              [key]: { link: item.offerLink, time: new Date().getTime() },
            };
            chrome.storage.local.set(obj);

            const requestOptions = {
              method: 'GET',
              headers: {
                'accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/json'
              },  
            };     

            fetch(item.offerLink,requestOptions)
              .then((response) => {
                console.log(ct(), "响应状态码：" + response.status); // 打印状态码
                if (response.status === 403) {
                  // 如果状态码是403，等待
                  pause_s_time();
                }
                if (response.status === 404) {
                  let offerRemove = {
                    cpId: item.id,
                    clientId: uniqueId,
                    reason:  "404错误"
                  };

                  // 如果状态码是404，发送cpId到REMOVE_URL并停止处理
                  console.log(
                    ct(),
                    `链接 ${item.offerLink} 返回404，将cpId${item.id} 报送给REMOVE_URL`
                  );
                  fetch(REMOVE_URL, {
                    method: "PUT",
                    headers: {
                      Accept: "application/json",
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(offerRemove),
                  })
                    .then((removeResponse) => removeResponse.json())
                    .then((removeData) => {
                      console.log(ct(), "REMOVE_URL 响应:", removeData);
                      // 这里可以添加额外的逻辑来处理REMOVE_URL的响应
                    })
                    .catch((removeError) => {
                      erpErrLog("发送到REMOVE_URL时出错:", removeError);
                    });
                  // 不再继续执行下面的代码，因为链接是404
                  return;
                }
                // 如果状态码不是404，继续处理响应体
                return response.json();
              })
              .then((data) => {
                // console.log(ct(), "数据抓取成功:", data);



                if (!data) {
                  // 如果data是undefined，不执行任何操作或进行错误处理
                  return;
                }
                var productHeader = {};
                if (data["allegro.showoffer.productHeader"] != undefined)
                    productHeader = data["allegro.showoffer.productHeader"];
                else
                    productHeader = data["showoffer.productHeader"];



                if(productHeader["offer"]["view"]["type"]==("ENDED")){
                  // 如果碰到销售结束，则删除数据
                  let offerRemove = {
                    cpId: item.id,
                    clientId: uniqueId,
                    reason:  "销售结束"
                  };

                  // 如果状态码是404，发送cpId到REMOVE_URL并停止处理
                  console.log(
                    ct(),
                    `链接 ${item.offerLink} 销售结束，将cpId${item.id} 报送给REMOVE_URL`
                  );
                  fetch(REMOVE_URL, {
                    method: "PUT",
                    headers: {
                      Accept: "application/json",
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(offerRemove),
                  })
                    .then((removeResponse) => removeResponse.json())
                    .then((removeData) => {
                      console.log(ct(), "REMOVE_URL 响应:", removeData);
                      // 这里可以添加额外的逻辑来处理REMOVE_URL的响应
                    })
                    .catch((removeError) => {
                      erpErrLog("发送到REMOVE_URL时出错:", removeError);
                    });
                  // 不再继续执行下面的代码，因为链接是404
                  return;

                }



             
                  if (productHeader) {

                    let offer = {
                      cpId: item.id,
                      offerJson: productHeader,
                      customJson:{},
                      clientId: uniqueId,
                    };

                    fetch(POSTOFFER_URL, {
                      method: "PUT",
                      headers: {
                        Accept: "application/json",
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify(offer),
                    })
                      .then((response) => response.json())
                      .then((data) => {
                        console.log(ct(), "数据保存成功:", data.msg);
                      })
                      .catch((error) => {
                        erpErrLog("数据保存失败:", error);
                      });
                  } else {
                    pause_s_time();
                    sendPopTips(
                      "遇到反爬,请开启或者更换代理或者暂停采集，稍后再采集!"
                    );
                  }
                
                chrome.storage.local.remove(key, function () {
                  console.log(ct(), `键名 ${key} 的值已被清空！`);
                });
              })
              .catch((error) => {
                chrome.storage.local.remove(key, function () {
                  erpErrLog(`数据抓取错误 ，键名 ${key} 的值已被清空！`, error);
                });
                pause_s_time();
                sendPopTips(
                  "遇到反爬,请验证,请开启或者更换代理或者暂停采集，稍后再采集!"
                );
              });
          });
        } else {
          erpErrLog("51ERP API请求失败:", data.msg);
        }
      })
      .catch((error) => {
        erpErrLog("请求错误:", error);
      });
  });
}

function request_shangjia_company_page_link() {
  chrome.storage.local.get(null, function (items) {
    let need_item = null;
    for (let key in items) {
      if (key.indexOf("shangjia_about_contact_and_basic_info_") != -1) {
        if (
          items[key]["_is_request"] == 2 &&
          items[key]["_seller_page_url"] &&
          items[key]["_seller_page_url_is_request"] == 0
        ) {
          need_item = items[key];
          need_item["_seller_page_url_is_request"] = 1;
          need_item["_seller_page_url_s_time"] = Date.now();
          let obj = {};
          obj[key] = need_item;
          chrome.storage.local.set(obj, function () {
            request_shangjia_company_page_link_ajax(key);
          });
          break;
        }
      }
    }
  });
}

function xintiao() {
  chrome.storage.local.get(null, function (items) {
    if (!items.hasOwnProperty("pause") || !items["pause"]) {
      if (
        items.hasOwnProperty("pause_s_time") &&
        items["pause_s_time"] + 600000 > Date.now()
      ) {
        console.log(ct(), "后台详情爬取失败，暂停10分钟");
      } else {
        console.log(ct(), "后台详情爬取中:");
        request_shangjia_about_contact_and_basic_info();
      }
    } else {
      console.log(ct(), "=========后台详情已暂停爬取================");
    }
  });

  // 20240731 新增
  if (Date.now() - lastUpdateTime > 3 * 60 * 1000) { // 3分钟 content.js 没动作，需要刷新一下前端活动页面。
    // chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    //     chrome.tabs.reload(tabs[0].id);
    // });

     chrome.tabs.query({currentWindow: true}, function(tabs) {
            tabs.forEach(function(tab) {
                // 可以在这里添加条件判断，只刷新特定标签页
                // 例如，只刷新包含特定网址的标签页
                 if (tab.url && tab.url.includes('amazon.com')) {
                    chrome.tabs.reload(tab.id);
                 }
            });
        });
        lastUpdateTime = Date.now() ; // 重置时间更新
    
    

}
}

function request_shangjia_about_contact_and_basic_info() {
  chrome.storage.local.get(null, function (items) {
    let binfa = 0;
    for (let key in items) {
      if (key.indexOf("request_offer_detail_") != -1) {
        if (items[key]["time"] + 300000 > Date.now()) {
          chrome.storage.local.remove(key, function () {
            console.log(ct(), `键名 ${key} 的值已被清空,5分钟未处理的数据！`);
          });
        }
        binfa++;
        if (binfa == 20) {
          break;
        }
      }
    }
    if (binfa < 20) {
      console.log(ct(), "当前并发数：" + binfa);
      request_offer();
    }
  });
}

function storage_shangjias_update(taskType,shangjia_infos = []) {
  chrome.storage.local.get(null, function (items) {
    let total_add_shangjia_num = items.hasOwnProperty("total_add_shangjia_num")
      ? items["total_add_shangjia_num"]
      : 0;
    total_add_shangjia_num += shangjia_infos.length;
    chrome.storage.local.set({
      total_add_shangjia_num: total_add_shangjia_num,
    });

    sendPutRequest(taskType,shangjia_infos)
      .then((responseData) => {
        console.log(ct(), "成功提交到后台:", responseData);
      })
      .catch((error) => {
        erpErrLog("Fetch error details:", error);
        erpErrLog("Error stack trace:", error.stack);
      });
  });
}

function sendPutRequest(taskType,data, retries = 3) {
  const headers = {
    "Content-Type": "application/json",
  };
  return new Promise((resolve, reject) => {
    function attempt(remainingRetries) {
      fetch(NEW_BATCH_URL+"/"+taskType, {
        method: "PUT",
        headers: headers,
        body: JSON.stringify(data),
      })
        .then((response) => response.json())
        .then(resolve)
        .catch((error) => {
          erpErrLog(`Attempt failed: ${error}`);
          if (remainingRetries > 0) {
            erpErrLog(
              `重试中... (${retries - remainingRetries + 1}/${retries})`
            );
            setTimeout(() => attempt(remainingRetries - 1), 20000);
            sendPopTips(
              "网络错误，请检查网络连接,数据提交不成功，请确认是否使用代理导致的"
            );
          } else {
            reject(new Error(`HTTP error! status: ${error}`));
          }
        });
    }
    attempt(retries);
  });
}

function storage_get(callback) {
  chrome.storage.local.get(null, callback);
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === "updateTime") {
    lastUpdateTime = Date.now();
    sendResponse({state: "time updated："+new Date(lastUpdateTime).toLocaleString()});
}else  if (request.action == "shangjia_items_update") {
    console.log("action", request, sender, sendResponse);
    let shangjia_infos = request.shangjia_infos;
    storage_shangjias_update(request.taskType, shangjia_infos);
    sendResponse({ state: "商品保存成功！" });
  } else if (request.action == "can_zidong_caiji") {
    chrome.storage.local.get(null, function (items) {
      if (!items.hasOwnProperty("pause_page") || items["pause_page"] == 0) {
        let total_add_shangjia_num = items.hasOwnProperty(
          "total_add_shangjia_num"
        )
          ? items["total_add_shangjia_num"]
          : 0;

        uniqueId = items.hasOwnProperty("uniqueId") ? items["uniqueId"] : "";
        console.log(ct(), "类目采集： uniqueId:" + uniqueId);

        var collect_item_num = items["collect_item_num"] || 10000;
        let msg = {
          state: 0,
          collect_item_num: collect_item_num,
          collect_zero_sales: items["collect_zero_sales"] || false,
          collect_min_price: items["collect_min_price"] || 50,
          collect_max_price: items["collect_max_price"] || 500,
          collect_max_page: items["collect_max_page"] || 100,
          uniqueId: uniqueId,
        };
        sendResponse(JSON.stringify(msg));
        

        // if (total_add_shangjia_num <= collect_item_num) {
          
        // } else {
        //   sendPopTips("商品达到上限，停止收集！");
        //   sendResponse(JSON.stringify({ state: 1 }));
        // }
      } else {
        sendResponse(JSON.stringify({ state: 1 }));
      }
    });
    return true;
  }
});

// 这个函数用于发送消息到弹窗
function sendPopTips(msg = "", url = "") {
  // 寻找一个连接是来自弹窗的端口
  // let popupPort = null;
  // chrome.runtime_ports.forEach((port) => {
  //     if (port.name === "popup") {
  //         popupPort = port;
  //     }
  // });
  // if (popupPort) {
  //     popupPort.postMessage({ action: "background-tips", info: msg, url: url });
  // } else {
  //     console.error("No popup port found");
  //     // 可能需要打开弹窗或创建连接
  // }
}

// 监听来自内容脚本或其他扩展部分的连接请求
chrome.runtime.onConnect.addListener(function (port) {
  if (port.name === "popup-to-background") {
    port.onMessage.addListener(function (msg) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message);
        return;
      }
      console.log(ct(), "后台接收消息， received:", msg);
      // 处理消息
    });
    port.onDisconnect.addListener(function () {
      console.log(ct(), "Disconnected from popup");
    });
  }
});

chrome.runtime.onInstalled.addListener(() => {
  console.log(ct(), "采集组件 onInstalled 我是后台js...");

  chrome.declarativeNetRequest.updateDynamicRules(
    {
      addRules: [
        {
          id: 31112,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.doubleclick.net/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        {
          id: 31113,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.facebook.com/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        {
          id: 31114,
          priority: 1,
          action: { type: "block" },
          condition: {
            urlFilter: "*://*.tiktok.com/*",
            domains: ["allegro.pl"],
            resourceTypes: ["script"],
          },
        },
        
      ],
    },
    function () {}
  );
});

xintiao(); // 立即执行一次心跳函数
setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数

chrome.runtime.onStartup.addListener(function () {
  console.log(ct(), "采集组件onStartup，我是后台js...");
  // xintiao(); // 立即执行一次心跳函数
  // setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数
});




function generateUniqueId() {
  var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  var result = "";
  for (var i = 0; i < 5; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function erpErrLog(msg) {
  console.error(ct() + msg);
}

function ct() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份是从0开始的
  var day = now.getDate();
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var seconds = now.getSeconds();

  // 补零函数
  function pad(number) {
    return (number < 10 ? "0" : "") + number;
  }

  // 返回自定义格式的日期时间字符串
  return (
    year +
    "-" +
    pad(month) +
    "-" +
    pad(day) +
    " " +
    pad(hours) +
    ":" +
    pad(minutes) +
    ":" +
    pad(seconds) +
    " "
  );
}
