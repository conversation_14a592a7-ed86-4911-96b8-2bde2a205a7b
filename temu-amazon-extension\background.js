// 导入配置文件
try {
  importScripts('config.js');
} catch (error) {
  console.warn('配置文件加载失败:', error);
}

// 获取配置
const backgroundConfig = (typeof AmazonCrawlerConfig !== 'undefined') ? AmazonCrawlerConfig : {};

// 基础配置 - 使用config.js中的配置
// BASE_URL 已在 config.js 中定义

// Amazon产品详情采集相关API - 使用config.js中的配置
var DETAIL_TASK_WAITGET_URL = backgroundConfig.API_ENDPOINTS?.DETAIL_TASK_WAITGET || "http://127.0.0.1:32000/api/amazon/page/task/waitGets";
var DETAIL_TASK_SUBMIT_URL = backgroundConfig.API_ENDPOINTS?.DETAIL_TASK_SUBMIT || "http://127.0.0.1:32000/api/amazon/page/task/submitResult";
var PRODUCT_DETAIL_SUBMIT_URL = backgroundConfig.API_ENDPOINTS?.PRODUCT_DETAIL_SUBMIT || "http://127.0.0.1:32000/api/amazon/page/task/submitSpuSku";

var uniqueId = "";
let lastUpdateTime = Date.now();


function util_get_random_number(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function pause_s_time() {
  chrome.storage.local.set({ pause_s_time: Date.now() });
}

// HTML处理函数已移除

// Amazon相关的请求函数已移除，只保留Amazon采集功能

// 商家信息采集相关函数已移除

function xintiao() {
  // Amazon页面自动刷新机制
  if (Date.now() - lastUpdateTime > 3 * 60 * 1000) { // 3分钟 content.js 没动作，需要刷新一下前端活动页面。
     chrome.tabs.query({currentWindow: true}, function(tabs) {
            tabs.forEach(function(tab) {
                // 只刷新Amazon页面
                 if (tab.url && tab.url.includes('amazon.com')) {
                    chrome.tabs.reload(tab.id);
                 }
            });
        });
        lastUpdateTime = Date.now() ; // 重置时间更新
    }
}

// 商家信息处理相关函数已移除，只保留Amazon采集功能

function storage_get(callback) {
  chrome.storage.local.get(null, callback);
}



// 处理API代理请求，解决CORS问题
async function handleProxyApiRequest(request, sendResponse) {
  try {
    const { method, url, data } = request;

    console.log(ct(), `代理API请求: ${method} ${url}`);

    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      // 检查是否包含压缩信息（仅用于日志记录）
      if (data.isCompressed && data.compressionInfo) {
        console.log(ct(), `收到包含压缩htmlContent的payload，压缩比: ${data.compressionInfo.ratio}%`);
        console.log(ct(), `压缩信息:`, {
          originalSize: data.compressionInfo.originalSize,
          compressedSize: data.compressionInfo.compressedSize,
          ratio: data.compressionInfo.ratio,
          method: data.compressionInfo.method
        });
      }

      options.body = JSON.stringify(data);

      // 记录请求体大小
      const bodySize = new TextEncoder().encode(options.body).length;
      console.log(ct(), `请求体大小: ${bodySize} bytes (${(bodySize/1024).toFixed(2)} KB)`);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();

    console.log(ct(), `代理API请求成功: ${method} ${url}`);

    sendResponse({
      success: true,
      data: responseData,
      status: response.status
    });

  } catch (error) {
    console.error(ct(), `代理API请求失败: ${error.message}`);

    sendResponse({
      success: false,
      error: error.message,
      status: error.status || 500
    });
  }
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === "updateTime") {
    lastUpdateTime = Date.now();
    sendResponse({state: "time updated："+new Date(lastUpdateTime).toLocaleString()});
  } else if (request.action === "proxy_api_request") {
    // 代理API请求，解决CORS问题
    handleProxyApiRequest(request, sendResponse);
    return true; // 异步响应
  } else if (request.action == "can_zidong_caiji") {
    // Amazon列表采集权限检查
    chrome.storage.local.get(null, function (items) {
      if (!items.hasOwnProperty("pause_page") || items["pause_page"] == 0) {
        uniqueId = items.hasOwnProperty("uniqueId") ? items["uniqueId"] : "";
        console.log(ct(), "Amazon列表采集： uniqueId:" + uniqueId);

        var collect_item_num = items["collect_item_num"] || 10000;
        let msg = {
          state: 0,
          collect_item_num: collect_item_num,
          collect_zero_sales: items["collect_zero_sales"] || false,
          collect_min_price: items["collect_min_price"] || 50,
          collect_max_price: items["collect_max_price"] || 500,
          collect_max_page: items["collect_max_page"] || 100,
          uniqueId: uniqueId,
        };
        sendResponse(JSON.stringify(msg));
      } else {
        sendResponse(JSON.stringify({ state: 1 }));
      }
    });
    return true;
  } else if (request.action == "start_detail_crawling") {
    // 启动Amazon产品详情采集
    chrome.storage.local.get(["uniqueId"], function (items) {
      const uniqueId = items.uniqueId || (backgroundConfig.generateUniqueId ? backgroundConfig.generateUniqueId() : generateUniqueId());
      chrome.storage.local.set({ uniqueId: uniqueId });

      // 启动采集循环（在后台运行）
      startAmazonDetailCrawlingLoop(uniqueId);

      sendResponse({ success: true, message: "Amazon产品详情采集已启动" });
    });
    return true;
  } else if (request.action == "process_single_product") {
    // 处理单个产品详情
    chrome.storage.local.get(["uniqueId"], async function (items) {
      const uniqueId = items.uniqueId || (backgroundConfig.generateUniqueId ? backgroundConfig.generateUniqueId() : generateUniqueId());

      try {
        const success = await processAmazonProductDetail(
          request.productUrl,
          request.taskId || 0,
          uniqueId
        );
        sendResponse({ success: success });
      } catch (error) {
        sendResponse({ success: false, error: error.message });
      }
    });
    return true;
  } else if (request.action == "stop_detail_crawling") {
    // 停止详情采集循环
    stopAmazonDetailCrawlingLoop();
    sendResponse({ success: true, message: "采集循环已停止" });
    return true;
  } else if (request.action == "get_crawling_stats") {
    // 获取采集统计信息
    sendResponse({ success: true, stats: getCrawlingStats() });
    return true;
  }
});

// 全局变量跟踪采集进度
let detailCrawlingStats = {
  isRunning: false,
  totalProcessed: 0,
  currentProduct: '',
  currentProductTitle: '',
  startTime: null,
  lastUpdateTime: null
};

// 弹窗消息函数已移除

/**
 * 更新采集进度并通知popup
 * @param {string} currentProduct 当前处理的产品ASIN
 * @param {string} currentProductTitle 当前处理的产品标题
 * @param {boolean} isCompleted 是否完成
 */
function updateCrawlingProgress(currentProduct = '', currentProductTitle = '', isCompleted = false) {
  if (isCompleted) {
    detailCrawlingStats.totalProcessed++;
  }

  detailCrawlingStats.currentProduct = currentProduct;
  detailCrawlingStats.currentProductTitle = currentProductTitle;
  detailCrawlingStats.lastUpdateTime = new Date().toISOString();

  // 保存到本地存储
  chrome.storage.local.set({ detailCrawlingStats: detailCrawlingStats });

  // 发送消息到popup（如果打开的话）
  try {
    chrome.runtime.sendMessage({
      action: 'detail_crawling_progress',
      stats: detailCrawlingStats
    });
  } catch (error) {
    // popup可能没有打开，忽略错误
  }

  console.log(`采集进度更新: 已处理${detailCrawlingStats.totalProcessed}个产品, 当前: ${currentProductTitle || currentProduct}`);
}

/**
 * 获取当前采集统计信息
 * @returns {Object} 采集统计信息
 */
function getCrawlingStats() {
  return { ...detailCrawlingStats };
}

// ==================== Amazon产品详情采集系统 ====================

/**
 * 获取待处理的Amazon产品详情采集任务
 * @param {string} clientId 客户端ID
 * @returns {Promise<Object|null>} 任务对象或null
 */
async function getPendingDetailTask(clientId) {
  try {
    const url = `${DETAIL_TASK_WAITGET_URL}?clientId=${clientId}`;
    console.log(ct(), `正在请求详情采集任务: ${url}`);

    const response = await fetch(url);
    console.log(ct(), `API响应状态: ${response.status}`);

    if (!response.ok) {
      throw new Error(`获取详情采集任务失败，状态码: ${response.status}`);
    }

    const data = await response.json();
    console.log(ct(), "获取到详情采集任务响应:", data);

    // 检查响应格式 - 支持两种格式
    if (data.code === 200 && data.data) {
      // 格式1: {code: 200, msg: "Success", data: {...}}
      const task = {
        ...data.data,
        productUrl: data.data.url // 后台返回url字段，前端使用productUrl
      };
      console.log(ct(), `解析到任务: ID=${task.id}, ASIN=${task.entryAsin}, URL=${task.productUrl}`);
      return task;
    } else if (data.success && data.data) {
      // 格式2: {success: true, data: {...}}
      const task = {
        ...data.data,
        productUrl: data.data.url // 后台返回url字段，前端使用productUrl
      };
      console.log(ct(), `解析到任务: ID=${task.id}, ASIN=${task.entryAsin}, URL=${task.productUrl}`);
      return task;
    }

    console.log(ct(), "没有待处理的详情采集任务");
    return null;
  } catch (error) {
    console.error(ct(), "获取详情采集任务失败:", error);
    return null;
  }
}

/**
 * 一次性提交完整的产品详情数据（SPU + SKU）
 * @param {Object} spuData SPU数据对象
 * @param {Array} skuDataList SKU数据数组
 * @returns {Promise<boolean>} 是否成功
 */
async function submitProductDetailData(spuData, skuDataList) {
  try {
    const payload = {
      spu: spuData,
      skuList: skuDataList,
      timestamp: new Date().toISOString(),
      totalSkuCount: skuDataList.length
    };

    console.log("提交产品详情数据:", {
      spuAsin: spuData.asin,
      skuCount: skuDataList.length,
      payload: payload
    });

    const response = await fetch(PRODUCT_DETAIL_SUBMIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`提交产品详情数据失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log(`产品详情数据提交响应:`, result);

    if (result.success) {
      console.log(`✅ 产品详情数据提交成功: SPU=${spuData.asin}, SKU数量=${skuDataList.length}`);
      if (result.data) {
        console.log(`处理结果: spuId=${result.data.spuId}, skuCount=${result.data.skuCount}, processedAt=${result.data.processedAt}`);
      }
      return true;
    } else {
      console.error("产品详情数据提交失败:", result.message || result.data);
      return false;
    }
  } catch (error) {
    console.error("提交产品详情数据失败:", error);
    return false;
  }
}

/**
 * 标记详情采集任务完成
 * @param {number} taskId 任务ID
 * @param {boolean} success 是否成功
 * @param {string} errorMessage 错误信息（如果失败）
 * @returns {Promise<boolean>} 是否成功
 */
async function markDetailTaskCompleted(taskId, success = true, errorMessage = '') {
  try {
    const payload = {
      taskId: taskId,
      status: success ? 'completed' : 'failed',
      errorMessage: errorMessage || '',
      completedAt: new Date().toISOString()
    };

    console.log("提交任务结果:", payload);

    const response = await fetch(DETAIL_TASK_SUBMIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`标记任务完成失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log("任务状态更新成功:", result);

    if (result.success) {
      return true;
    } else {
      console.error("任务状态更新失败:", result.message || result.data);
      return false;
    }
  } catch (error) {
    console.error("标记任务完成失败:", error);
    return false;
  }
}

// Amazon解析函数已移至amazon-parser.js模块

// 以下Amazon解析函数已移至amazon-parser.js模块：
// - extractVariationData()
// - isMultiVariantProduct()
// - extractSkuDataFromVariations()
// - createSingleSkuFromPage()
// - fetchIndividualSkuDetails()
// - requestSkuDetailsFromContentScript()
// - generateMockPrice()

// extractSkuDataFromVariations函数已移至amazon-parser.js

// createSingleSkuFromPage函数已移至amazon-parser.js

// fetchIndividualSkuDetails函数已移至amazon-parser.js

// requestSkuDetailsFromContentScript和generateMockPrice函数已移至amazon-parser.js

/**
 * 主要的Amazon产品详情采集流程
 * @param {string} productUrl 产品详情页URL
 * @param {number} taskId 任务ID
 * @param {string} clientId 客户端ID
 * @returns {Promise<boolean>} 是否成功
 */
async function processAmazonProductDetail(productUrl, taskId, clientId, taskData = null) {
  console.log(`开始处理Amazon产品详情: ${productUrl} (任务ID: ${taskId}, 客户端: ${clientId})`);

  try {
    // 1. 验证URL格式
    if (!productUrl || !productUrl.includes('amazon.com/dp/')) {
      throw new Error("无效的Amazon产品URL");
    }

    // 2. 更新进度 - 开始处理
    const asinMatch = productUrl.match(/\/dp\/([A-Z0-9]{10})/);
    const currentAsin = taskData?.entryAsin || (asinMatch ? asinMatch[1] : 'UNKNOWN');
    updateCrawlingProgress(currentAsin, '正在获取页面数据...', false);

    // 3. 获取页面内容（通过content script）
    const pageData = await requestPageDataFromContentScript(productUrl);
    if (!pageData || !pageData.html) {
      throw new Error("无法获取页面数据，请确保在Amazon页面运行");
    }

    // 4. 解析页面内容
    const parser = new DOMParser();
    const doc = parser.parseFromString(pageData.html, 'text/html');

    // 5. 提取SPU数据（使用amazon-parser.js中的函数）
    console.log("正在提取SPU数据...");
    updateCrawlingProgress(currentAsin, '正在提取产品基础信息...', false);
    const spuData = AmazonParser.extractSpuData(doc, pageData.url, taskData);
    if (!spuData.asin || spuData.asin === 'UNKNOWN') {
      throw new Error("无法提取产品ASIN，可能页面结构不正确");
    }

    // 6. 更新进度 - 显示产品标题
    updateCrawlingProgress(spuData.asin, spuData.title || taskData?.listPageTitle || '未知产品', false);

    // 7. 提取变体数据（使用amazon-parser.js中的函数）
    console.log("正在提取变体数据...");
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 检测变体信息', false);
    const variationData = AmazonParser.extractVariationData(doc);

    // 8. 判断是否为多变体产品并提取SKU数据（使用amazon-parser.js中的函数）
    let skuDataList = [];

    if (AmazonParser.isMultiVariantProduct(variationData)) {
      console.log(`检测到多变体产品，父ASIN: ${spuData.asin}`);
      updateCrawlingProgress(spuData.asin, spuData.title + ' - 处理多变体产品', false);
      skuDataList = AmazonParser.extractSkuDataFromVariations(variationData, spuData.asin);

      // 获取每个SKU的详细信息（暂时跳过，因为需要重构）
      // if (skuDataList.length > 0) {
      //   console.log(`开始获取 ${skuDataList.length} 个SKU的详细信息...`);
      //   updateCrawlingProgress(spuData.asin, spuData.title + ` - 获取${skuDataList.length}个变体详情`, false);
      //   skuDataList = await AmazonParser.fetchIndividualSkuDetails(skuDataList);
      // }
    } else {
      console.log(`检测到单变体产品，ASIN: ${spuData.asin}`);
      updateCrawlingProgress(spuData.asin, spuData.title + ' - 处理单变体产品', false);
      skuDataList = AmazonParser.createSingleSkuFromPage(doc, spuData.asin);
    }

    // 7. 验证数据完整性
    if (skuDataList.length === 0) {
      console.warn("未提取到任何SKU数据，创建默认SKU");
      skuDataList = [{
        asin: spuData.asin,
        parentAsin: spuData.asin,
        currency: 'USD',
        stockStatus: 'Unknown',
        price: null,
        imageUrl: spuData.mainImageUrl,
        variationAttributes: JSON.stringify({})
      }];
    }

    // 9. 一次性上传SPU和SKU数据
    console.log(`正在上传产品详情数据: SPU=${spuData.asin}, SKU数量=${skuDataList.length}...`);
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 上传数据到后台', false);
    const submitSuccess = await submitProductDetailData(spuData, skuDataList);
    if (!submitSuccess) {
      throw new Error("产品详情数据上传失败");
    }

    // 10. 标记任务完成
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, true);
    }

    // 11. 更新进度 - 完成
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 采集完成', true);

    console.log(`✅ 产品详情采集完成: ${spuData.asin}, SPU: 1个, SKU: ${skuDataList.length}个`);
    return true;

  } catch (error) {
    console.error("❌ 处理Amazon产品详情失败:", error);

    // 标记任务失败
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, false, error.message);
    }
    return false;
  }
}

/**
 * 向content script请求页面数据
 * @param {string} url 页面URL
 * @returns {Promise<Object|null>} 页面数据
 */
async function requestPageDataFromContentScript(url) {
  return new Promise((resolve) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        resolve(null);
        return;
      }

      const tabId = tabs[0].id;

      chrome.tabs.sendMessage(tabId, {
        action: 'getPageData',
        url: url
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn("请求页面数据失败:", chrome.runtime.lastError.message);
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });
  });
}

/**
 * Amazon产品详情采集任务循环
 * @param {string} clientId 客户端ID
 */
async function startAmazonDetailCrawlingLoop(clientId) {
  console.log("启动Amazon产品详情采集任务循环...");

  // 初始化采集状态
  detailCrawlingStats.isRunning = true;
  detailCrawlingStats.startTime = new Date().toISOString();
  detailCrawlingStats.totalProcessed = 0;
  updateCrawlingProgress('', '正在启动采集循环...', false);

  while (detailCrawlingStats.isRunning) {
    try {
      // 获取待处理的任务
      updateCrawlingProgress('', '正在获取待处理任务...', false);
      const task = await getPendingDetailTask(clientId);

      if (!task) {
        console.log("暂无待处理的详情采集任务，等待30秒后重试...");
        updateCrawlingProgress('', '暂无任务，等待中...', false);
        await new Promise(resolve => setTimeout(resolve, 30000));
        continue;
      }

      console.log(`获取到详情采集任务: ${task.id} - ${task.productUrl || task.url}`);
      console.log(`任务详情: entryAsin=${task.entryAsin}, listPageTitle=${task.listPageTitle}, status=${task.status}`);

      // 处理任务
      const success = await processAmazonProductDetail(task.productUrl || task.url, task.id, clientId, task);

      if (success) {
        console.log(`任务 ${task.id} 处理成功`);
      } else {
        console.log(`任务 ${task.id} 处理失败`);
      }

      // 随机等待10-15秒后处理下一个任务
      const waitTime = Math.random() * 5000 + 10000; // 10-15秒
      console.log(`等待 ${Math.round(waitTime/1000)} 秒后处理下一个任务...`);
      updateCrawlingProgress('', `等待${Math.round(waitTime/1000)}秒后继续...`, false);
      await new Promise(resolve => setTimeout(resolve, waitTime));

    } catch (error) {
      console.error("任务循环出错:", error);
      updateCrawlingProgress('', '出现错误，等待重试...', false);

      // 出错后等待60秒再重试
      console.log("等待60秒后重试...");
      await new Promise(resolve => setTimeout(resolve, 60000));
    }
  }

  console.log("Amazon产品详情采集任务循环已停止");
  updateCrawlingProgress('', '采集循环已停止', false);
}

/**
 * 停止Amazon产品详情采集循环
 */
function stopAmazonDetailCrawlingLoop() {
  console.log("正在停止Amazon产品详情采集循环...");
  detailCrawlingStats.isRunning = false;
  updateCrawlingProgress('', '正在停止采集循环...', false);
}

// ==================== 自动启动功能 ====================

/**
 * 检查是否应该自动启动详情采集
 */
async function checkAutoStartDetailCrawling() {
  try {
    const items = await chrome.storage.local.get(['auto_start_detail_crawling', 'uniqueId']);

    if (items.auto_start_detail_crawling === true && !detailCrawlingStats.isRunning) {
      const uniqueId = items.uniqueId || generateUniqueId();
      console.log("检测到自动启动设置，开始启动详情采集循环...");

      // 延迟5秒后启动，确保系统完全初始化
      setTimeout(() => {
        startAmazonDetailCrawlingLoop(uniqueId);
      }, 5000);
    }
  } catch (error) {
    console.error("检查自动启动设置失败:", error);
  }
}

// 扩展启动时检查自动启动设置
chrome.runtime.onStartup.addListener(() => {
  console.log("Chrome扩展启动，检查自动启动设置...");
  checkAutoStartDetailCrawling();
});

// 扩展安装或更新时检查自动启动设置
chrome.runtime.onInstalled.addListener(() => {
  console.log("Chrome扩展安装/更新，检查自动启动设置...");
  checkAutoStartDetailCrawling();
});

// Service Worker启动时检查自动启动设置
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log("Service Worker启动，检查自动启动设置...");
  checkAutoStartDetailCrawling();
}

// 手动启动详情采集的全局函数（用于调试）
window.startDetailCrawling = function() {
  console.log("手动启动Amazon详情采集循环...");
  chrome.storage.local.get(['uniqueId'], function(items) {
    const uniqueId = items.uniqueId || generateUniqueId();
    chrome.storage.local.set({ uniqueId: uniqueId });

    if (!detailCrawlingStats.isRunning) {
      startAmazonDetailCrawlingLoop(uniqueId);
      console.log("详情采集循环已启动，uniqueId:", uniqueId);
    } else {
      console.log("详情采集循环已在运行中");
    }
  });
};

// 停止详情采集的全局函数（用于调试）
window.stopDetailCrawling = function() {
  console.log("手动停止Amazon详情采集循环...");
  stopAmazonDetailCrawlingLoop();
};

// 获取采集状态的全局函数（用于调试）
window.getCrawlingStatus = function() {
  console.log("当前采集状态:", detailCrawlingStats);
  return detailCrawlingStats;
};

// 监听来自内容脚本或其他扩展部分的连接请求
chrome.runtime.onConnect.addListener(function (port) {
  if (port.name === "popup-to-background") {
    port.onMessage.addListener(function (msg) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message);
        return;
      }
      console.log(ct(), "后台接收消息， received:", msg);
      // 处理消息
    });
    port.onDisconnect.addListener(function () {
      console.log(ct(), "Disconnected from popup");
    });
  }
});

chrome.runtime.onInstalled.addListener(() => {
  console.log(ct(), "Amazon采集插件 onInstalled 我是后台js...");

  // 清理可能存在的旧规则
  chrome.declarativeNetRequest.getDynamicRules((existingRules) => {
    const ruleIdsToRemove = existingRules.map(rule => rule.id);

    chrome.declarativeNetRequest.updateDynamicRules(
      {
        removeRuleIds: ruleIdsToRemove,
        addRules: [
          {
            id: 88001, // 使用更独特的ID避免冲突
            priority: 1,
            action: { type: "block" },
            condition: {
              urlFilter: "*://*.doubleclick.net/*",
              domains: ["amazon.com"],
              resourceTypes: ["script"],
            },
          },
          {
            id: 88002,
            priority: 1,
            action: { type: "block" },
            condition: {
              urlFilter: "*://googletagmanager.com/*",
              domains: ["amazon.com"],
              resourceTypes: ["script"],
            },
          },
          {
            id: 88003,
            priority: 1,
            action: { type: "block" },
            condition: {
              urlFilter: "*://google-analytics.com/*",
              domains: ["amazon.com"],
              resourceTypes: ["script"],
            },
          },
        ],
      },
      function () {
        if (chrome.runtime.lastError) {
          console.warn(ct(), "更新declarativeNetRequest规则失败:", chrome.runtime.lastError.message);
        } else {
          console.log(ct(), "declarativeNetRequest规则更新成功");
        }
      }
    );
  });
});

xintiao(); // 立即执行一次心跳函数
setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数

chrome.runtime.onStartup.addListener(function () {
  console.log(ct(), "采集组件onStartup，我是后台js...");
  // xintiao(); // 立即执行一次心跳函数
  // setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数
});




// 后备的generateUniqueId函数（如果config.js未加载）
function generateUniqueId() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `client_${timestamp}_${random}_v1.0.0`;
}

function erpErrLog(msg) {
  console.error(ct() + msg);
}

function ct() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份是从0开始的
  var day = now.getDate();
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var seconds = now.getSeconds();

  // 补零函数
  function pad(number) {
    return (number < 10 ? "0" : "") + number;
  }

  // 返回自定义格式的日期时间字符串
  return (
    year +
    "-" +
    pad(month) +
    "-" +
    pad(day) +
    " " +
    pad(hours) +
    ":" +
    pad(minutes) +
    ":" +
    pad(seconds) +
    " "
  );
}
