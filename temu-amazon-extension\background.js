// 导入配置文件
try {
  importScripts('config.js');
} catch (error) {
  console.warn('配置文件加载失败:', error);
}

// 获取配置
const backgroundConfig = (typeof AmazonCrawlerConfig !== 'undefined') ? AmazonCrawlerConfig : {};

// 基础配置 - 使用config.js中的配置
// BASE_URL 已在 config.js 中定义

// Amazon产品详情采集相关API - 使用config.js中的配置
var DETAIL_TASK_WAITGET_URL = backgroundConfig.API_ENDPOINTS?.DETAIL_TASK_WAITGET || "http://127.0.0.1:32000/api/amazon/page/task/waitGets";
var DETAIL_TASK_SUBMIT_URL = backgroundConfig.API_ENDPOINTS?.DETAIL_TASK_SUBMIT || "http://127.0.0.1:32000/api/amazon/page/task/submitResult";
var PRODUCT_DETAIL_SUBMIT_URL = backgroundConfig.API_ENDPOINTS?.PRODUCT_DETAIL_SUBMIT || "http://127.0.0.1:32000/api/amazon/page/task/submitSpuSku";

var uniqueId = "";
let lastUpdateTime = Date.now();


function util_get_random_number(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function pause_s_time() {
  chrome.storage.local.set({ pause_s_time: Date.now() });
}

// HTML处理函数已移除

// Amazon相关的请求函数已移除，只保留Amazon采集功能

// 商家信息采集相关函数已移除

function xintiao() {
  // Amazon页面自动刷新机制
  if (Date.now() - lastUpdateTime > 3 * 60 * 1000) { // 3分钟 content.js 没动作，需要刷新一下前端活动页面。
     chrome.tabs.query({currentWindow: true}, function(tabs) {
            tabs.forEach(function(tab) {
                // 只刷新Amazon页面
                 if (tab.url && tab.url.includes('amazon.com')) {
                    chrome.tabs.reload(tab.id);
                 }
            });
        });
        lastUpdateTime = Date.now() ; // 重置时间更新
    }
}

// 商家信息处理相关函数已移除，只保留Amazon采集功能

function storage_get(callback) {
  chrome.storage.local.get(null, callback);
}



// 处理API代理请求，解决CORS问题
async function handleProxyApiRequest(request, sendResponse) {
  try {
    const { method, url, data } = request;

    console.log(ct(), `代理API请求: ${method} ${url}`);

    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      // 检查是否包含压缩信息（仅用于日志记录）
      if (data.isCompressed && data.compressionInfo) {
        console.log(ct(), `收到包含压缩htmlContent的payload，压缩比: ${data.compressionInfo.ratio}%`);
        console.log(ct(), `压缩信息:`, {
          originalSize: data.compressionInfo.originalSize,
          compressedSize: data.compressionInfo.compressedSize,
          ratio: data.compressionInfo.ratio,
          method: data.compressionInfo.method
        });
      }

      options.body = JSON.stringify(data);

      // 记录请求体大小
      const bodySize = new TextEncoder().encode(options.body).length;
      console.log(ct(), `请求体大小: ${bodySize} bytes (${(bodySize/1024).toFixed(2)} KB)`);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();

    console.log(ct(), `代理API请求成功: ${method} ${url}`);

    sendResponse({
      success: true,
      data: responseData,
      status: response.status
    });

  } catch (error) {
    console.error(ct(), `代理API请求失败: ${error.message}`);

    sendResponse({
      success: false,
      error: error.message,
      status: error.status || 500
    });
  }
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === "updateTime") {
    lastUpdateTime = Date.now();
    sendResponse({state: "time updated："+new Date(lastUpdateTime).toLocaleString()});
  } else if (request.action === "proxy_api_request") {
    // 代理API请求，解决CORS问题
    handleProxyApiRequest(request, sendResponse);
    return true; // 异步响应
  } else if (request.action == "can_zidong_caiji") {
    // Amazon列表采集权限检查
    chrome.storage.local.get(null, function (items) {
      if (!items.hasOwnProperty("pause_page") || items["pause_page"] == 0) {
        uniqueId = items.hasOwnProperty("uniqueId") ? items["uniqueId"] : "";
        console.log(ct(), "Amazon列表采集： uniqueId:" + uniqueId);

        var collect_item_num = items["collect_item_num"] || 10000;
        let msg = {
          state: 0,
          collect_item_num: collect_item_num,
          collect_zero_sales: items["collect_zero_sales"] || false,
          collect_min_price: items["collect_min_price"] || 50,
          collect_max_price: items["collect_max_price"] || 500,
          collect_max_page: items["collect_max_page"] || 100,
          uniqueId: uniqueId,
        };
        sendResponse(JSON.stringify(msg));
      } else {
        sendResponse(JSON.stringify({ state: 1 }));
      }
    });
    return true;
  } else if (request.action == "start_detail_crawling") {
    // 启动Amazon产品详情采集
    chrome.storage.local.get(["uniqueId"], function (items) {
      const uniqueId = items.uniqueId || (backgroundConfig.generateUniqueId ? backgroundConfig.generateUniqueId() : generateUniqueId());
      chrome.storage.local.set({ uniqueId: uniqueId });

      // 启动采集循环（在后台运行）
      startAmazonDetailCrawlingLoop(uniqueId);

      sendResponse({ success: true, message: "Amazon产品详情采集已启动" });
    });
    return true;
  } else if (request.action == "process_single_product") {
    // 处理单个产品详情
    chrome.storage.local.get(["uniqueId"], async function (items) {
      const uniqueId = items.uniqueId || (backgroundConfig.generateUniqueId ? backgroundConfig.generateUniqueId() : generateUniqueId());

      try {
        const success = await processAmazonProductDetail(
          request.productUrl,
          request.taskId || 0,
          uniqueId
        );
        sendResponse({ success: success });
      } catch (error) {
        sendResponse({ success: false, error: error.message });
      }
    });
    return true;
  } else if (request.action == "stop_detail_crawling") {
    // 停止详情采集循环
    stopAmazonDetailCrawlingLoop();
    sendResponse({ success: true, message: "采集循环已停止" });
    return true;
  } else if (request.action == "get_crawling_stats") {
    // 获取采集统计信息
    sendResponse({ success: true, stats: getCrawlingStats() });
    return true;
  }
});

// 全局变量跟踪采集进度
let detailCrawlingStats = {
  isRunning: false,
  totalProcessed: 0,
  currentProduct: '',
  currentProductTitle: '',
  startTime: null,
  lastUpdateTime: null
};

// 弹窗消息函数已移除

/**
 * 更新采集进度并通知popup
 * @param {string} currentProduct 当前处理的产品ASIN
 * @param {string} currentProductTitle 当前处理的产品标题
 * @param {boolean} isCompleted 是否完成
 */
function updateCrawlingProgress(currentProduct = '', currentProductTitle = '', isCompleted = false) {
  if (isCompleted) {
    detailCrawlingStats.totalProcessed++;
  }

  detailCrawlingStats.currentProduct = currentProduct;
  detailCrawlingStats.currentProductTitle = currentProductTitle;
  detailCrawlingStats.lastUpdateTime = new Date().toISOString();

  // 保存到本地存储
  chrome.storage.local.set({ detailCrawlingStats: detailCrawlingStats });

  // 发送消息到popup（如果打开的话）
  try {
    chrome.runtime.sendMessage({
      action: 'detail_crawling_progress',
      stats: detailCrawlingStats
    });
  } catch (error) {
    // popup可能没有打开，忽略错误
  }

  console.log(`采集进度更新: 已处理${detailCrawlingStats.totalProcessed}个产品, 当前: ${currentProductTitle || currentProduct}`);
}

/**
 * 获取当前采集统计信息
 * @returns {Object} 采集统计信息
 */
function getCrawlingStats() {
  return { ...detailCrawlingStats };
}

// ==================== Amazon产品详情采集系统 ====================

/**
 * 获取待处理的Amazon产品详情采集任务
 * @param {string} clientId 客户端ID
 * @returns {Promise<Object|null>} 任务对象或null
 */
async function getPendingDetailTask(clientId) {
  try {
    const url = `${DETAIL_TASK_WAITGET_URL}?clientId=${clientId}`;
    console.log(ct(), `正在请求详情采集任务: ${url}`);

    const response = await fetch(url);
    console.log(ct(), `API响应状态: ${response.status}`);

    if (!response.ok) {
      throw new Error(`获取详情采集任务失败，状态码: ${response.status}`);
    }

    const data = await response.json();
    console.log(ct(), "获取到详情采集任务响应:", data);

    // 检查响应格式 - 支持两种格式
    if (data.code === 200 && data.data) {
      // 格式1: {code: 200, msg: "Success", data: {...}}
      const task = {
        ...data.data,
        productUrl: data.data.url // 后台返回url字段，前端使用productUrl
      };
      console.log(ct(), `解析到任务: ID=${task.id}, ASIN=${task.entryAsin}, URL=${task.productUrl}`);
      return task;
    } else if (data.success && data.data) {
      // 格式2: {success: true, data: {...}}
      const task = {
        ...data.data,
        productUrl: data.data.url // 后台返回url字段，前端使用productUrl
      };
      console.log(ct(), `解析到任务: ID=${task.id}, ASIN=${task.entryAsin}, URL=${task.productUrl}`);
      return task;
    }

    console.log(ct(), "没有待处理的详情采集任务");
    return null;
  } catch (error) {
    console.error(ct(), "获取详情采集任务失败:", error);
    return null;
  }
}

/**
 * 一次性提交完整的产品详情数据（SPU + SKU）
 * @param {Object} spuData SPU数据对象
 * @param {Array} skuDataList SKU数据数组
 * @returns {Promise<boolean>} 是否成功
 */
async function submitProductDetailData(spuData, skuDataList) {
  try {
    const payload = {
      spu: spuData,
      skuList: skuDataList,
      timestamp: new Date().toISOString(),
      totalSkuCount: skuDataList.length
    };

    console.log("提交产品详情数据:", {
      spuAsin: spuData.asin,
      skuCount: skuDataList.length,
      payload: payload
    });

    const response = await fetch(PRODUCT_DETAIL_SUBMIT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`提交产品详情数据失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log(`产品详情数据提交响应:`, result);

    if (result.success) {
      console.log(`✅ 产品详情数据提交成功: SPU=${spuData.asin}, SKU数量=${skuDataList.length}`);
      if (result.data) {
        console.log(`处理结果: spuId=${result.data.spuId}, skuCount=${result.data.skuCount}, processedAt=${result.data.processedAt}`);
      }
      return true;
    } else {
      console.error("产品详情数据提交失败:", result.message || result.data);
      return false;
    }
  } catch (error) {
    console.error("提交产品详情数据失败:", error);
    return false;
  }
}

/**
 * 标记详情采集任务完成
 * @param {number} taskId 任务ID
 * @param {boolean} success 是否成功
 * @param {string} errorMessage 错误信息（如果失败）
 * @returns {Promise<boolean>} 是否成功
 */
async function markDetailTaskCompleted(taskId, success = true, errorMessage = '') {
  try {
    const payload = {
      taskId: taskId,
      status: success ? 'completed' : 'failed',
      errorMessage: errorMessage || '',
      completedAt: new Date().toISOString()
    };

    console.log("提交任务结果:", payload);

    const response = await fetch(DETAIL_TASK_SUBMIT_URL, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`标记任务完成失败，状态码: ${response.status}`);
    }

    const result = await response.json();
    console.log("任务状态更新成功:", result);

    if (result.success) {
      return true;
    } else {
      console.error("任务状态更新失败:", result.message || result.data);
      return false;
    }
  } catch (error) {
    console.error("标记任务完成失败:", error);
    return false;
  }
}

// Amazon解析函数已移至amazon-parser.js模块

// 以下Amazon解析函数已移至amazon-parser.js模块：
// - extractVariationData()
// - isMultiVariantProduct()
// - extractSkuDataFromVariations()
// - createSingleSkuFromPage()
// - fetchIndividualSkuDetails()
// - requestSkuDetailsFromContentScript()
// - generateMockPrice()

// extractSkuDataFromVariations函数已移至amazon-parser.js

// createSingleSkuFromPage函数已移至amazon-parser.js

// fetchIndividualSkuDetails函数已移至amazon-parser.js

// requestSkuDetailsFromContentScript和generateMockPrice函数已移至amazon-parser.js

/**
 * 主要的Amazon产品详情采集流程
 * @param {string} productUrl 产品详情页URL
 * @param {number} taskId 任务ID
 * @param {string} clientId 客户端ID
 * @returns {Promise<boolean>} 是否成功
 */
async function processAmazonProductDetail(productUrl, taskId, clientId, taskData = null) {
  console.log(`开始处理Amazon产品详情: ${productUrl} (任务ID: ${taskId}, 客户端: ${clientId})`);

  try {
    // 1. 验证URL格式
    if (!productUrl || !productUrl.includes('amazon.com/dp/')) {
      throw new Error("无效的Amazon产品URL");
    }

    // 2. 更新进度 - 开始处理
    const asinMatch = productUrl.match(/\/dp\/([A-Z0-9]{10})/);
    const currentAsin = taskData?.entryAsin || (asinMatch ? asinMatch[1] : 'UNKNOWN');
    updateCrawlingProgress(currentAsin, '正在获取页面数据...', false);

    // 3. 直接发起HTTP请求获取页面HTML
    console.log("正在获取产品页面HTML...");
    updateCrawlingProgress(currentAsin, '正在获取页面数据...', false);

    const pageHtml = await fetchAmazonPageHtml(productUrl);
    if (!pageHtml) {
      throw new Error("无法获取页面HTML数据");
    }

    // 4. 解析页面HTML（使用正则表达式，因为Service Worker中没有DOMParser）
    console.log("正在解析产品页面...");
    updateCrawlingProgress(currentAsin, '正在解析产品数据...', false);

    // 5. 提取SPU数据
    console.log("正在提取SPU数据...");
    updateCrawlingProgress(currentAsin, '正在提取产品基础信息...', false);
    const spuData = extractSpuDataFromHtml(pageHtml, productUrl, taskData);

    if (!spuData.asin || spuData.asin === 'UNKNOWN') {
      throw new Error("无法提取产品ASIN，可能页面结构不正确");
    }

    // 6. 更新进度 - 显示产品标题
    updateCrawlingProgress(spuData.asin, spuData.title || taskData?.listPageTitle || '未知产品', false);

    // 7. 提取变体数据
    console.log("正在提取变体数据...");
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 检测变体信息', false);
    const variationData = extractVariationDataFromHtml(pageHtml);

    // 8. 判断是否为多变体产品并提取SKU数据
    let skuDataList = [];

    if (isMultiVariantProduct(variationData)) {
      console.log(`检测到多变体产品，父ASIN: ${spuData.asin}`);
      updateCrawlingProgress(spuData.asin, spuData.title + ' - 处理多变体产品', false);

      // 从变体数据中提取所有SKU的ASIN
      const skuAsins = extractSkuAsinsFromVariations(variationData);
      console.log(`找到 ${skuAsins.length} 个SKU变体:`, skuAsins);

      // 并发获取每个SKU的详细信息
      if (skuAsins.length > 0) {
        updateCrawlingProgress(spuData.asin, spuData.title + ` - 获取${skuAsins.length}个变体详情`, false);
        skuDataList = await fetchMultipleSkuDetails(skuAsins, spuData.asin, variationData);
      }
    } else {
      console.log(`检测到单变体产品，ASIN: ${spuData.asin}`);
      updateCrawlingProgress(spuData.asin, spuData.title + ' - 处理单变体产品', false);
      skuDataList = createSingleSkuFromHtml(pageHtml, spuData.asin);
    }

    // 9. 验证数据完整性
    if (skuDataList.length === 0) {
      console.warn("未提取到任何SKU数据，创建默认SKU");
      skuDataList = [{
        asin: spuData.asin,
        parentAsin: spuData.asin,
        currency: 'USD',
        stockStatus: 'Unknown',
        price: null,
        imageUrl: spuData.mainImageUrl,
        variationAttributes: JSON.stringify({})
      }];
    }

    console.log(`产品数据解析完成: SPU=${spuData.asin}, SKU数量=${skuDataList.length}`);

    // 9. 一次性上传SPU和SKU数据
    console.log(`正在上传产品详情数据: SPU=${spuData.asin}, SKU数量=${skuDataList.length}...`);
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 上传数据到后台', false);
    const submitSuccess = await submitProductDetailData(spuData, skuDataList);
    if (!submitSuccess) {
      throw new Error("产品详情数据上传失败");
    }

    // 10. 标记任务完成
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, true);
    }

    // 11. 更新进度 - 完成
    updateCrawlingProgress(spuData.asin, spuData.title + ' - 采集完成', true);

    console.log(`✅ 产品详情采集完成: ${spuData.asin}, SPU: 1个, SKU: ${skuDataList.length}个`);
    return true;

  } catch (error) {
    console.error("❌ 处理Amazon产品详情失败:", error);

    // 标记任务失败
    if (taskId > 0) {
      await markDetailTaskCompleted(taskId, false, error.message);
    }
    return false;
  }
}

// ==================== Amazon页面数据获取和解析函数 ====================

/**
 * 获取Amazon页面HTML
 * @param {string} url 页面URL
 * @returns {Promise<string|null>} 页面HTML
 */
async function fetchAmazonPageHtml(url) {
  try {
    console.log(ct(), `正在获取页面: ${url}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    console.log(ct(), `页面获取成功，HTML长度: ${html.length}`);
    return html;

  } catch (error) {
    console.error(ct(), `获取页面失败: ${url}`, error);
    return null;
  }
}

/**
 * Amazon产品详情采集任务循环
 * @param {string} clientId 客户端ID
 */
async function startAmazonDetailCrawlingLoop(clientId) {
  console.log("启动Amazon产品详情采集任务循环...");

  // 初始化采集状态
  detailCrawlingStats.isRunning = true;
  detailCrawlingStats.startTime = new Date().toISOString();
  detailCrawlingStats.totalProcessed = 0;
  updateCrawlingProgress('', '正在启动采集循环...', false);

  while (detailCrawlingStats.isRunning) {
    try {
      // 获取待处理的任务
      updateCrawlingProgress('', '正在获取待处理任务...', false);
      const task = await getPendingDetailTask(clientId);

      if (!task) {
        console.log("暂无待处理的详情采集任务，等待30秒后重试...");
        updateCrawlingProgress('', '暂无任务，等待中...', false);
        await new Promise(resolve => setTimeout(resolve, 30000));
        continue;
      }

      console.log(`获取到详情采集任务: ${task.id} - ${task.productUrl || task.url}`);
      console.log(`任务详情: entryAsin=${task.entryAsin}, listPageTitle=${task.listPageTitle}, status=${task.status}`);

      // 处理任务
      const success = await processAmazonProductDetail(task.productUrl || task.url, task.id, clientId, task);

      if (success) {
        console.log(`任务 ${task.id} 处理成功`);
      } else {
        console.log(`任务 ${task.id} 处理失败`);
      }

      // 随机等待10-15秒后处理下一个任务
      const waitTime = Math.random() * 5000 + 10000; // 10-15秒
      console.log(`等待 ${Math.round(waitTime/1000)} 秒后处理下一个任务...`);
      updateCrawlingProgress('', `等待${Math.round(waitTime/1000)}秒后继续...`, false);
      await new Promise(resolve => setTimeout(resolve, waitTime));

    } catch (error) {
      console.error("任务循环出错:", error);
      updateCrawlingProgress('', '出现错误，等待重试...', false);

      // 出错后等待60秒再重试
      console.log("等待60秒后重试...");
      await new Promise(resolve => setTimeout(resolve, 60000));
    }
  }

  console.log("Amazon产品详情采集任务循环已停止");
  updateCrawlingProgress('', '采集循环已停止', false);
}

/**
 * 停止Amazon产品详情采集循环
 */
function stopAmazonDetailCrawlingLoop() {
  console.log("正在停止Amazon产品详情采集循环...");
  detailCrawlingStats.isRunning = false;
  updateCrawlingProgress('', '正在停止采集循环...', false);
}

// ==================== 自动启动功能 ====================

/**
 * 检查是否应该自动启动详情采集
 */
async function checkAutoStartDetailCrawling() {
  try {
    const items = await chrome.storage.local.get(['auto_start_detail_crawling', 'uniqueId']);

    if (items.auto_start_detail_crawling === true && !detailCrawlingStats.isRunning) {
      const uniqueId = items.uniqueId || generateUniqueId();
      console.log("检测到自动启动设置，开始启动详情采集循环...");

      // 延迟5秒后启动，确保系统完全初始化
      setTimeout(() => {
        startAmazonDetailCrawlingLoop(uniqueId);
      }, 5000);
    }
  } catch (error) {
    console.error("检查自动启动设置失败:", error);
  }
}

// 扩展启动时检查自动启动设置
chrome.runtime.onStartup.addListener(() => {
  console.log("Chrome扩展启动，检查自动启动设置...");
  checkAutoStartDetailCrawling();
});

// 扩展安装或更新时检查自动启动设置
chrome.runtime.onInstalled.addListener(() => {
  console.log("Chrome扩展安装/更新，检查自动启动设置...");
  checkAutoStartDetailCrawling();
});

// Service Worker启动时检查自动启动设置
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log("Service Worker启动，检查自动启动设置...");
  checkAutoStartDetailCrawling();
}

// 手动启动详情采集的全局函数（用于调试）
globalThis.startDetailCrawling = function() {
  console.log("手动启动Amazon详情采集循环...");
  chrome.storage.local.get(['uniqueId'], function(items) {
    const uniqueId = items.uniqueId || generateUniqueId();
    chrome.storage.local.set({ uniqueId: uniqueId });

    if (!detailCrawlingStats.isRunning) {
      startAmazonDetailCrawlingLoop(uniqueId);
      console.log("详情采集循环已启动，uniqueId:", uniqueId);
    } else {
      console.log("详情采集循环已在运行中");
    }
  });
};

// 停止详情采集的全局函数（用于调试）
globalThis.stopDetailCrawling = function() {
  console.log("手动停止Amazon详情采集循环...");
  stopAmazonDetailCrawlingLoop();
};

// 获取采集状态的全局函数（用于调试）
globalThis.getCrawlingStatus = function() {
  console.log("当前采集状态:", detailCrawlingStats);
  return detailCrawlingStats;
};

// 监听来自内容脚本或其他扩展部分的连接请求
chrome.runtime.onConnect.addListener(function (port) {
  if (port.name === "popup-to-background") {
    port.onMessage.addListener(function (msg) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message);
        return;
      }
      console.log(ct(), "后台接收消息， received:", msg);
      // 处理消息
    });
    port.onDisconnect.addListener(function () {
      console.log(ct(), "Disconnected from popup");
    });
  }
});

chrome.runtime.onInstalled.addListener(() => {
  console.log(ct(), "Amazon采集插件 onInstalled 我是后台js...");

  // 清理可能存在的旧规则
  chrome.declarativeNetRequest.getDynamicRules((existingRules) => {
    const ruleIdsToRemove = existingRules.map(rule => rule.id);

    chrome.declarativeNetRequest.updateDynamicRules(
      {
        removeRuleIds: ruleIdsToRemove,
        addRules: [
          {
            id: 88001, // 使用更独特的ID避免冲突
            priority: 1,
            action: { type: "block" },
            condition: {
              urlFilter: "*://*.doubleclick.net/*",
              domains: ["amazon.com"],
              resourceTypes: ["script"],
            },
          },
          {
            id: 88002,
            priority: 1,
            action: { type: "block" },
            condition: {
              urlFilter: "*://googletagmanager.com/*",
              domains: ["amazon.com"],
              resourceTypes: ["script"],
            },
          },
          {
            id: 88003,
            priority: 1,
            action: { type: "block" },
            condition: {
              urlFilter: "*://google-analytics.com/*",
              domains: ["amazon.com"],
              resourceTypes: ["script"],
            },
          },
        ],
      },
      function () {
        if (chrome.runtime.lastError) {
          console.warn(ct(), "更新declarativeNetRequest规则失败:", chrome.runtime.lastError.message);
        } else {
          console.log(ct(), "declarativeNetRequest规则更新成功");
        }
      }
    );
  });
});

xintiao(); // 立即执行一次心跳函数
setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数

chrome.runtime.onStartup.addListener(function () {
  console.log(ct(), "采集组件onStartup，我是后台js...");
  // xintiao(); // 立即执行一次心跳函数
  // setInterval(xintiao, 10000); // 每10秒重复执行一次心跳函数
});




// 后备的generateUniqueId函数（如果config.js未加载）
function generateUniqueId() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `client_${timestamp}_${random}_v1.0.0`;
}

function erpErrLog(msg) {
  console.error(ct() + msg);
}

function ct() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份是从0开始的
  var day = now.getDate();
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var seconds = now.getSeconds();

  // 补零函数
  function pad(number) {
    return (number < 10 ? "0" : "") + number;
  }

  // 返回自定义格式的日期时间字符串
  return (
    year +
    "-" +
    pad(month) +
    "-" +
    pad(day) +
    " " +
    pad(hours) +
    ":" +
    pad(minutes) +
    ":" +
    pad(seconds) +
    " "
  );
}

// ==================== Amazon页面解析函数 ====================

/**
 * 从HTML字符串中提取SPU数据
 * @param {string} html 页面HTML字符串
 * @param {string} pageUrl 页面URL
 * @param {Object} taskData 任务数据
 * @returns {Object} SPU数据对象
 */
function extractSpuDataFromHtml(html, pageUrl = '', taskData = null) {
  console.log(ct(), "开始提取SPU数据...");

  const spuData = {};

  // 1. 提取ASIN（优先级：任务entryAsin > parentAsin > data-asin > URL）
  spuData.asin = extractAsinFromHtml(html, pageUrl, taskData);

  // 2. 提取基础信息
  spuData.title = extractTitleFromHtml(html);
  spuData.brand = extractBrandFromHtml(html);
  spuData.rating = extractRatingFromHtml(html);
  spuData.reviewCount = extractReviewCountFromHtml(html);

  // 3. 提取图片信息
  spuData.mainImageUrl = extractMainImageFromHtml(html);
  spuData.imageUrls = extractImageUrlsFromHtml(html);

  // 4. 提取描述信息
  spuData.bulletPoints = extractBulletPointsFromHtml(html);
  spuData.description = extractDescriptionFromHtml(html);

  // 5. 提取产品详情和规格
  const { productDetails, productAttributes } = extractProductDetailsFromHtml(html);
  spuData.productDetails = JSON.stringify(productDetails);
  spuData.productAttributes = JSON.stringify(productAttributes);

  // 6. 提取类目路径
  spuData.categoryPath = extractCategoryPathFromHtml(html);

  // 7. 组装Temu格式的产品详情
  spuData.temuProductDetail = buildTemuProductDetail(spuData, productAttributes);

  console.log(ct(), "SPU数据提取完成:", spuData.asin, "-", spuData.title?.substring(0, 50) + "...");
  return spuData;
}

/**
 * 从HTML字符串中提取变体数据
 * @param {string} html 页面HTML字符串
 * @returns {Object} 变体数据对象
 */
function extractVariationDataFromHtml(html) {
  console.log(ct(), "开始提取变体数据...");

  const variationData = {};

  // 使用正则表达式提取script标签内容
  const scriptPattern = /<script[^>]*>([\s\S]*?)<\/script>/gi;
  let scriptMatch;

  while ((scriptMatch = scriptPattern.exec(html)) !== null) {
    const scriptContent = scriptMatch[1];
    if (!scriptContent || !scriptContent.includes('dimensionToAsinMap')) {
      continue;
    }

    try {
      // 提取dimensionToAsinMap
      const dimensionMatch = scriptContent.match(/"dimensionToAsinMap"\s*:\s*({[^}]+})/);
      if (dimensionMatch) {
        variationData.dimensionToAsinMap = JSON.parse(dimensionMatch[1]);
        console.log(ct(), "找到dimensionToAsinMap:", variationData.dimensionToAsinMap);
      }

      // 提取variationValues
      const variationMatch = scriptContent.match(/"variationValues"\s*:\s*({[^}]+})/);
      if (variationMatch) {
        variationData.variationValues = JSON.parse(variationMatch[1]);
        console.log(ct(), "找到variationValues:", variationData.variationValues);
      }

      // 提取colorToAsin
      const colorToAsinMatch = scriptContent.match(/"colorToAsin"\s*:\s*({[^}]+})/);
      if (colorToAsinMatch) {
        variationData.colorToAsin = JSON.parse(colorToAsinMatch[1]);
        console.log(ct(), "找到colorToAsin:", variationData.colorToAsin);
      }

      // 提取colorImages
      const colorImagesMatch = scriptContent.match(/"colorImages"\s*:\s*({[^}]+})/);
      if (colorImagesMatch) {
        try {
          variationData.colorImages = JSON.parse(colorImagesMatch[1]);
          console.log(ct(), "找到colorImages，变体数量:", Object.keys(variationData.colorImages).length);
        } catch (e) {
          console.warn(ct(), "解析colorImages失败:", e);
        }
      }

      // 提取parentAsin
      const parentMatch = scriptContent.match(/"parentAsin"\s*:\s*"([^"]+)"/);
      if (parentMatch) {
        variationData.parentAsin = parentMatch[1];
        console.log(ct(), "找到parentAsin:", parentMatch[1]);
      }

      break; // 找到数据后退出循环
    } catch (error) {
      console.warn(ct(), "解析变体数据时出错:", error);
      continue;
    }
  }

  return variationData;
}

/**
 * 判断是否为多变体产品
 * @param {Object} variationData 变体数据
 * @returns {boolean} 是否为多变体
 */
function isMultiVariantProduct(variationData) {
  const hasMultipleDimensions = variationData.dimensionToAsinMap &&
                                Object.keys(variationData.dimensionToAsinMap).length > 1;
  const hasMultipleColors = variationData.colorToAsin &&
                           Object.keys(variationData.colorToAsin).length > 1;

  return hasMultipleDimensions || hasMultipleColors;
}

/**
 * 从变体数据中提取所有SKU的ASIN
 * @param {Object} variationData 变体数据
 * @returns {Array} SKU ASIN数组
 */
function extractSkuAsinsFromVariations(variationData) {
  const skuAsins = [];

  if (variationData.dimensionToAsinMap) {
    for (const asin of Object.values(variationData.dimensionToAsinMap)) {
      if (asin && !skuAsins.includes(asin)) {
        skuAsins.push(asin);
      }
    }
  }

  if (variationData.colorToAsin) {
    for (const asin of Object.values(variationData.colorToAsin)) {
      if (asin && !skuAsins.includes(asin)) {
        skuAsins.push(asin);
      }
    }
  }

  return skuAsins;
}

/**
 * 并发获取多个SKU的详细信息
 * @param {Array} skuAsins SKU ASIN数组
 * @param {string} parentAsin 父ASIN
 * @param {Object} variationData 变体数据
 * @returns {Promise<Array>} SKU数据数组
 */
async function fetchMultipleSkuDetails(skuAsins, parentAsin, variationData) {
  console.log(ct(), `开始并发获取 ${skuAsins.length} 个SKU的详细信息...`);

  const skuDataList = [];
  const maxConcurrent = 3; // 最大并发数

  // 分批处理，避免过多并发请求
  for (let i = 0; i < skuAsins.length; i += maxConcurrent) {
    const batch = skuAsins.slice(i, i + maxConcurrent);
    const batchPromises = batch.map(async (asin) => {
      try {
        const skuUrl = `https://www.amazon.com/dp/${asin}`;
        const skuHtml = await fetchAmazonPageHtml(skuUrl);

        if (skuHtml) {
          return createSkuDataFromHtml(skuHtml, asin, parentAsin, variationData);
        }
        return null;
      } catch (error) {
        console.warn(ct(), `获取SKU ${asin} 详情失败:`, error);
        return null;
      }
    });

    const batchResults = await Promise.all(batchPromises);

    // 添加成功获取的SKU数据
    for (const skuData of batchResults) {
      if (skuData) {
        skuDataList.push(skuData);
      }
    }

    // 批次间等待，避免请求过快
    if (i + maxConcurrent < skuAsins.length) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  console.log(ct(), `SKU详情获取完成，成功获取 ${skuDataList.length}/${skuAsins.length} 个SKU`);
  return skuDataList;
}

/**
 * 从HTML字符串创建SKU数据
 * @param {string} html 页面HTML字符串
 * @param {string} asin SKU ASIN
 * @param {string} parentAsin 父ASIN
 * @param {Object} variationData 变体数据
 * @returns {Object} SKU数据对象
 */
function createSkuDataFromHtml(html, asin, parentAsin, variationData) {
  const skuData = {
    asin: asin,
    parentAsin: parentAsin,
    currency: 'USD',
    stockStatus: 'In Stock',
    price: null,
    imageUrl: null,
    variationAttributes: {}
  };

  // 提取价格
  skuData.price = extractPriceFromHtml(html);

  // 提取主图
  skuData.imageUrl = extractMainImageFromHtml(html);

  // 提取库存状态
  skuData.stockStatus = extractStockStatusFromHtml(html);

  // 解析变体属性
  const variationAttributes = parseVariationAttributesForSku(asin, variationData);
  skuData.variationAttributes = JSON.stringify(variationAttributes);

  return skuData;
}

/**
 * 为单变体产品创建SKU数据
 * @param {string} html 页面HTML字符串
 * @param {string} parentAsin 父ASIN
 * @returns {Array} 包含单个SKU的数组
 */
function createSingleSkuFromHtml(html, parentAsin) {
  console.log(ct(), "创建单变体SKU数据...");

  const skuData = {
    asin: parentAsin,
    parentAsin: parentAsin,
    currency: 'USD',
    stockStatus: 'In Stock',
    price: null,
    imageUrl: null,
    variationAttributes: JSON.stringify({})
  };

  // 提取价格
  skuData.price = extractPriceFromHtml(html);

  // 提取主图
  skuData.imageUrl = extractMainImageFromHtml(html);

  // 提取库存状态
  skuData.stockStatus = extractStockStatusFromHtml(html);

  console.log(ct(), "单变体SKU创建完成");
  return [skuData];
}

// ==================== 基础提取函数（基于HTML字符串） ====================

/**
 * 提取ASIN
 */
function extractAsinFromHtml(html, pageUrl, taskData) {
  // 优先使用任务中的entryAsin
  if (taskData && taskData.entryAsin) {
    console.log(ct(), "使用任务entryAsin作为SPU主键:", taskData.entryAsin);
    return taskData.entryAsin;
  }

  // 尝试从JavaScript中提取parentAsin
  const parentMatch = html.match(/"parentAsin"\s*:\s*"([^"]+)"/);
  if (parentMatch) {
    console.log(ct(), "使用parentAsin作为SPU主键:", parentMatch[1]);
    return parentMatch[1];
  }

  // 从页面元素中提取data-asin
  const dataAsinMatch = html.match(/data-asin="([A-Z0-9]{10})"/);
  if (dataAsinMatch) {
    console.log(ct(), "使用data-asin作为SPU主键:", dataAsinMatch[1]);
    return dataAsinMatch[1];
  }

  // 从URL中提取ASIN
  const urlMatch = pageUrl.match(/\/dp\/([A-Z0-9]{10})/);
  const asin = urlMatch ? urlMatch[1] : 'UNKNOWN';
  console.log(ct(), "使用URL中的ASIN作为SPU主键:", asin);
  return asin;
}

/**
 * 提取产品标题
 */
function extractTitleFromHtml(html) {
  const titleMatch = html.match(/<span[^>]*id="productTitle"[^>]*>(.*?)<\/span>/s);
  return titleMatch ? titleMatch[1].trim().replace(/<[^>]*>/g, '') : null;
}

/**
 * 提取品牌信息
 */
function extractBrandFromHtml(html) {
  const brandMatch = html.match(/<a[^>]*id="bylineInfo"[^>]*>(.*?)<\/a>/s);
  if (brandMatch) {
    const brandText = brandMatch[1].replace(/<[^>]*>/g, '').trim();
    const storeMatch = brandText.match(/Visit the (.+?) Store/);
    return storeMatch ? storeMatch[1] : brandText;
  }
  return null;
}

/**
 * 提取评分
 */
function extractRatingFromHtml(html) {
  const ratingMatch = html.match(/<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([^<]*out of[^<]*)<\/span>/);
  if (ratingMatch) {
    const ratingText = ratingMatch[1];
    const numberMatch = ratingText.match(/(\d+\.?\d*)/);
    return numberMatch ? parseFloat(numberMatch[1]) : null;
  }
  return null;
}

/**
 * 提取评价数量
 */
function extractReviewCountFromHtml(html) {
  const reviewMatch = html.match(/<span[^>]*id="acrCustomerReviewText"[^>]*>([^<]*)<\/span>/);
  if (reviewMatch) {
    const reviewText = reviewMatch[1].replace(/,/g, '');
    const numberMatch = reviewText.match(/(\d+)/);
    return numberMatch ? parseInt(numberMatch[1]) : null;
  }
  return null;
}

/**
 * 提取主图URL
 */
function extractMainImageFromHtml(html) {
  // 尝试多种模式匹配主图
  const patterns = [
    /<img[^>]*id="landingImage"[^>]*src="([^"]*)"[^>]*>/,
    /<img[^>]*id="imgBlkFront"[^>]*src="([^"]*)"[^>]*>/,
    /<img[^>]*data-old-hires="([^"]*)"[^>]*>/
  ];

  for (const pattern of patterns) {
    const match = html.match(pattern);
    if (match) {
      return match[1];
    }
  }
  return null;
}

/**
 * 提取所有图片URLs
 */
function extractImageUrlsFromHtml(html) {
  const imageUrls = [];
  const imagePattern = /<img[^>]*src="([^"]*amazon[^"]*)"[^>]*>/g;
  let match;

  while ((match = imagePattern.exec(html)) !== null) {
    const imgUrl = match[1];
    if (imgUrl && !imageUrls.includes(imgUrl)) {
      imageUrls.push(imgUrl);
    }
  }

  return JSON.stringify(imageUrls.slice(0, 10)); // 最多10张图片
}

/**
 * 提取五点描述
 */
function extractBulletPointsFromHtml(html) {
  const bulletPoints = [];
  const bulletPattern = /<span[^>]*class="[^"]*a-list-item[^"]*"[^>]*>(.*?)<\/span>/gs;
  let match;

  while ((match = bulletPattern.exec(html)) !== null) {
    const text = match[1].replace(/<[^>]*>/g, '').trim();
    if (text && text.length > 10 && !text.includes('Make sure')) {
      bulletPoints.push(text);
    }
  }

  return JSON.stringify(bulletPoints.slice(0, 5)); // 最多5个要点
}

/**
 * 提取商品描述
 */
function extractDescriptionFromHtml(html) {
  const descPatterns = [
    /<div[^>]*id="productDescription"[^>]*>(.*?)<\/div>/s,
    /<div[^>]*id="aplus"[^>]*>(.*?)<\/div>/s
  ];

  for (const pattern of descPatterns) {
    const match = html.match(pattern);
    if (match) {
      const desc = match[1].replace(/<[^>]*>/g, '').trim();
      return desc.substring(0, 2000); // 最多2000字符
    }
  }
  return null;
}

/**
 * 提取产品详情和技术规格
 */
function extractProductDetailsFromHtml(html) {
  const productDetails = {};
  const productAttributes = {};

  // 提取表格中的产品详情
  const tablePattern = /<table[^>]*class="[^"]*prodDetTable[^"]*"[^>]*>(.*?)<\/table>/gs;
  let tableMatch;

  while ((tableMatch = tablePattern.exec(html)) !== null) {
    const tableContent = tableMatch[1];
    const rowPattern = /<tr[^>]*>(.*?)<\/tr>/gs;
    let rowMatch;

    while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
      const rowContent = rowMatch[1];
      const cellPattern = /<td[^>]*>(.*?)<\/td>/gs;
      const cells = [];
      let cellMatch;

      while ((cellMatch = cellPattern.exec(rowContent)) !== null) {
        cells.push(cellMatch[1].replace(/<[^>]*>/g, '').trim());
      }

      if (cells.length >= 2) {
        const key = cells[0].replace(':', '').trim();
        const value = cells[1].trim();
        if (key && value && key !== 'Customer Reviews') {
          productDetails[key] = value;
          productAttributes[key] = value;
        }
      }
    }
  }

  return { productDetails, productAttributes };
}

/**
 * 提取类目路径
 */
function extractCategoryPathFromHtml(html) {
  const categoryPath = [];
  const breadcrumbPattern = /<a[^>]*href="[^"]*"[^>]*>([^<]*)<\/a>/g;
  let match;

  // 查找面包屑导航区域
  const breadcrumbSection = html.match(/<div[^>]*id="wayfinding-breadcrumbs_feature_div"[^>]*>(.*?)<\/div>/s);
  if (breadcrumbSection) {
    const sectionContent = breadcrumbSection[1];

    while ((match = breadcrumbPattern.exec(sectionContent)) !== null) {
      const text = match[1].trim();
      if (text && text !== 'Home' && text !== 'Amazon.com') {
        categoryPath.push(text);
      }
    }
  }

  return categoryPath.join(' > ');
}

/**
 * 提取价格
 */
function extractPriceFromHtml(html) {
  const pricePatterns = [
    /<span[^>]*class="[^"]*a-price[^"]*a-offscreen[^"]*"[^>]*>([^<]*)<\/span>/,
    /<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$([^<]*)<\/span>/
  ];

  for (const pattern of pricePatterns) {
    const match = html.match(pattern);
    if (match) {
      const priceText = match[1].replace('$', '').replace(',', '');
      const price = parseFloat(priceText);
      if (!isNaN(price)) {
        return price;
      }
    }
  }
  return null;
}

/**
 * 提取库存状态
 */
function extractStockStatusFromHtml(html) {
  const availabilityPatterns = [
    /<div[^>]*id="availability"[^>]*>(.*?)<\/div>/s,
    /<div[^>]*id="availabilityInsideBuyBox_feature_div"[^>]*>(.*?)<\/div>/s
  ];

  for (const pattern of availabilityPatterns) {
    const match = html.match(pattern);
    if (match) {
      const text = match[1].replace(/<[^>]*>/g, '').trim().toLowerCase();
      if (text.includes('in stock')) return 'In Stock';
      if (text.includes('out of stock')) return 'Out of Stock';
      if (text.includes('temporarily unavailable')) return 'Temporarily Unavailable';
    }
  }

  return 'Unknown';
}

/**
 * 提取评分
 */
function extractRatingFromDoc(doc) {
  const ratingElement = doc.querySelector('.a-icon-star-mini .a-icon-alt, .a-icon-star .a-icon-alt');
  if (ratingElement) {
    const ratingText = ratingElement.textContent.trim();
    const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
    return ratingMatch ? parseFloat(ratingMatch[1]) : null;
  }
  return null;
}

/**
 * 提取评价数量
 */
function extractReviewCountFromDoc(doc) {
  const reviewElement = doc.querySelector('#acrCustomerReviewText');
  if (reviewElement) {
    const reviewText = reviewElement.textContent.trim();
    const reviewMatch = reviewText.replace(/,/g, '').match(/(\d+)/);
    return reviewMatch ? parseInt(reviewMatch[1]) : null;
  }
  return null;
}

/**
 * 提取主图URL
 */
function extractMainImageFromDoc(doc) {
  const mainImageElement = doc.querySelector('#landingImage, #imgBlkFront');
  if (mainImageElement) {
    return mainImageElement.src || mainImageElement.getAttribute('data-src');
  }
  return null;
}

/**
 * 提取所有图片URLs
 */
function extractImageUrlsFromDoc(doc) {
  const imageUrls = [];
  const imageElements = doc.querySelectorAll('#altImages img, .a-carousel img');

  for (const img of imageElements) {
    const imgUrl = img.src || img.getAttribute('data-src');
    if (imgUrl && imgUrl.includes('amazon.com')) {
      imageUrls.push(imgUrl);
    }
  }

  return JSON.stringify(imageUrls.slice(0, 10)); // 最多10张图片
}

/**
 * 提取五点描述
 */
function extractBulletPointsFromDoc(doc) {
  const bulletPoints = [];
  const bulletElements = doc.querySelectorAll('#featurebullets_feature_div li span.a-list-item');

  for (const bullet of bulletElements) {
    const text = bullet.textContent.trim();
    if (text && text.length > 10) { // 过滤掉太短的文本
      bulletPoints.push(text);
    }
  }

  return JSON.stringify(bulletPoints.slice(0, 5)); // 最多5个要点
}

/**
 * 提取商品描述
 */
function extractDescriptionFromDoc(doc) {
  const descElement = doc.querySelector('#productDescription, #aplus');
  if (descElement) {
    return descElement.textContent.trim().substring(0, 2000); // 最多2000字符
  }
  return null;
}

/**
 * 提取产品详情和技术规格
 */
function extractProductDetailsFromDoc(doc) {
  const productDetails = {};
  const productAttributes = {};

  // 从产品详情表格中提取
  const detailSelectors = [
    '#prodDetails table.prodDetTable tr',
    '#productDetails_detailBullets_sections1 tr',
    '#productDetails_expanderTables tr',
    '#technicalSpecifications_section_1 tr'
  ];

  for (const selector of detailSelectors) {
    const detailElements = doc.querySelectorAll(selector);

    for (const row of detailElements) {
      const { key, value } = extractTableRowData(row);

      if (key && value && key !== 'Customer Reviews') {
        const cleanKey = key.replace(':', '').trim();
        productDetails[cleanKey] = value;
        productAttributes[cleanKey] = value;
      }
    }
  }

  return { productDetails, productAttributes };
}

/**
 * 提取表格行数据
 */
function extractTableRowData(row) {
  const cells = row.querySelectorAll('td');
  let key, value;

  if (cells.length >= 2) {
    key = cells[0].textContent.trim();
    value = cells[1].textContent.trim();
  } else {
    const thElement = row.querySelector('th');
    const tdElement = row.querySelector('td');
    if (thElement && tdElement) {
      key = thElement.textContent.trim();
      value = tdElement.textContent.trim();
    }
  }

  return { key, value };
}

/**
 * 提取类目路径
 */
function extractCategoryPathFromDoc(doc) {
  const breadcrumbElements = doc.querySelectorAll('#wayfinding-breadcrumbs_feature_div a');
  const categoryPath = [];

  for (const breadcrumb of breadcrumbElements) {
    const text = breadcrumb.textContent.trim();
    if (text && text !== 'Home' && text !== 'Amazon.com') {
      categoryPath.push(text);
    }
  }

  return categoryPath.join(' > ');
}

/**
 * 提取价格
 */
function extractPriceFromDoc(doc) {
  const priceElement = doc.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    const priceText = priceElement.textContent.trim();
    const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/);
    if (priceMatch) {
      const price = parseFloat(priceMatch[0].replace(',', ''));
      return price;
    }
  }
  return null;
}

/**
 * 提取库存状态
 */
function extractStockStatusFromDoc(doc) {
  const availabilitySelectors = [
    '#availability span',
    '#availabilityInsideBuyBox_feature_div span',
    '.a-color-success',
    '.a-color-state'
  ];

  for (const selector of availabilitySelectors) {
    const element = doc.querySelector(selector);
    if (element) {
      const text = element.textContent.trim().toLowerCase();
      if (text.includes('in stock')) return 'In Stock';
      if (text.includes('out of stock')) return 'Out of Stock';
      if (text.includes('temporarily unavailable')) return 'Temporarily Unavailable';
    }
  }

  return 'Unknown';
}

/**
 * 组装Temu格式的产品详情
 */
function buildTemuProductDetail(spuData, productAttributes) {
  const temuProductDetail = {
    basic_info: {
      title: spuData.title || '',
      brand: spuData.brand || '',
      rating: spuData.rating,
      review_count: spuData.reviewCount
    },
    media: {
      main_image: spuData.mainImageUrl || '',
      images: JSON.parse(spuData.imageUrls || '[]')
    },
    description: {
      bullet_points: JSON.parse(spuData.bulletPoints || '[]'),
      long_description: spuData.description || ''
    },
    specifications: productAttributes,
    category: {
      path: spuData.categoryPath || ''
    }
  };

  return JSON.stringify(temuProductDetail);
}

/**
 * 解析SKU的变体属性
 */
function parseVariationAttributesForSku(asin, variationData) {
  const attributes = {};

  // 从colorToAsin中查找颜色属性
  if (variationData.colorToAsin) {
    for (const [color, colorAsin] of Object.entries(variationData.colorToAsin)) {
      if (colorAsin === asin) {
        attributes.Color = color;
        break;
      }
    }
  }

  // 从dimensionToAsinMap中查找其他属性
  if (variationData.dimensionToAsinMap && variationData.variationValues) {
    const sizeNames = variationData.variationValues.size_name || [];

    for (const [index, dimensionAsin] of Object.entries(variationData.dimensionToAsinMap)) {
      if (dimensionAsin === asin) {
        const indexNum = parseInt(index);
        if (sizeNames[indexNum]) {
          attributes.Size = sizeNames[indexNum];
        }
        break;
      }
    }
  }

  return attributes;
}
