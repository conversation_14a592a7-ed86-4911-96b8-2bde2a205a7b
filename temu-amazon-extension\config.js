/**
 * Amazon产品详情采集系统配置文件
 * 统一管理所有API接口URL和系统配置
 */

// ==================== API配置 ====================

// 基础API地址
 //const BASE_URL = "https://51erp.store/xace-web/api/amazon";
const BASE_URL = "http://127.0.0.1:32000/api/amazon";

// Amazon产品详情采集相关API
const API_ENDPOINTS = {
  // 列表采集相关
  LIST_TASK_WAITGET: BASE_URL + "/list/task/waitGets",
  LIST_TASK_SUBMIT: BASE_URL + "/list/task/submitForm",
  
  // 详情采集相关
  DETAIL_TASK_WAITGET: BASE_URL + "/page/task/waitGets",
  DETAIL_TASK_SUBMIT: BASE_URL + "/page/task/submitResult",
  PRODUCT_DETAIL_SUBMIT: BASE_URL + "/page/task/submitSpuSku",
  
  // 传统采集相关（兼容性）
  TASK_WAITGET: BASE_URL + "/list/task/waitGets",
  TASK_SUBMIT: BASE_URL + "/task/submitResult",
  BATCH_SUBMIT: BASE_URL + "/collect/batch",
  NEW_BATCH_SUBMIT: BASE_URL + "/collect/newSellerCollect",
  REMOVE_ITEM: BASE_URL + "/collect/remove",
  CATE_WAITGET: BASE_URL + "/task/waitGets",
  CATE_SUBMIT: BASE_URL + "/task/putTaskJson"
};

// ==================== 系统配置 ====================

const SYSTEM_CONFIG = {
  // 客户端配置
  CLIENT_VERSION: "v1.0.0",
  
  // 采集配置
  CRAWLING: {
    // 任务间隔时间（毫秒）
    TASK_INTERVAL_MIN: 10000,  // 10秒
    TASK_INTERVAL_MAX: 15000,  // 15秒
    
    // SKU请求延迟（毫秒）
    SKU_REQUEST_DELAY_MIN: 1000,  // 1秒
    SKU_REQUEST_DELAY_MAX: 3000,  // 3秒
    
    // 请求超时时间（毫秒）
    REQUEST_TIMEOUT: 15000,  // 15秒
    
    // 重试配置
    MAX_RETRY_COUNT: 3,
    RETRY_DELAY: 60000,  // 60秒
    
    // 数据限制
    MAX_IMAGES: 10,
    MAX_BULLET_POINTS: 5,
    MAX_DESCRIPTION_LENGTH: 2000
  },
  
  // 存储配置
  STORAGE: {
    KEYS: {
      UNIQUE_ID: "uniqueId",
      AUTO_START_DETAIL: "auto_start_detail_crawling",
      DETAIL_STATS: "detailCrawlingStats",
      LIST_TASK: "listTask",
      TASK: "task",
      PAUSE_PAGE: "pause_page"
    }
  },
  
  // 进度更新配置
  PROGRESS: {
    UPDATE_INTERVAL: 2000,  // 2秒
    BROADCAST_ENABLED: true
  }
};

// ==================== Amazon页面配置 ====================

const AMAZON_CONFIG = {
  // 域名配置
  DOMAINS: [
    "amazon.com",
    "www.amazon.com",
    "amazon.co.uk",
    "amazon.de",
    "amazon.fr",
    "amazon.it",
    "amazon.es",
    "amazon.ca",
    "amazon.com.au",
    "amazon.co.jp"
  ],
  
  // URL模式
  URL_PATTERNS: {
    PRODUCT_DETAIL: /\/dp\/([A-Z0-9]{10})/,
    SEARCH_RESULTS: /\/s\?/,
    CATEGORY: /\/b\?/
  },
  
  // CSS选择器配置
  SELECTORS: {
    // 产品基础信息
    PRODUCT_TITLE: "#productTitle",
    BRAND_INFO: "#bylineInfo",
    RATING: ".a-icon-star-mini .a-icon-alt, .a-icon-star .a-icon-alt",
    REVIEW_COUNT: "#acrCustomerReviewText",
    
    // 图片相关
    MAIN_IMAGE: "#landingImage, #imgBlkFront",
    ALT_IMAGES: "#altImages img, .a-carousel img",
    
    // 价格相关
    PRICE: ".a-price .a-offscreen",
    
    // 库存状态
    AVAILABILITY: [
      "#availability span",
      "#availabilityInsideBuyBox_feature_div span",
      ".a-color-success",
      ".a-color-state",
      ".a-color-price"
    ],
    
    // 产品详情
    BULLET_POINTS: "#featurebullets_feature_div li span.a-list-item",
    DESCRIPTION: "#productDescription, #aplus",
    
    // 产品规格表格
    DETAIL_TABLES: [
      "#prodDetails table.prodDetTable tr",
      "#productDetails_detailBullets_sections1 tr",
      "#productDetails_expanderTables tr",
      "#technicalSpecifications_section_1 tr"
    ],
    
    // 类目路径
    BREADCRUMBS: "#wayfinding-breadcrumbs_feature_div a",
    
    // 搜索结果
    SEARCH_RESULTS_CONTAINER: 'span[data-component-type="s-search-results"]',
    
    // 分页
    PAGINATION: [
      ".a-pagination .a-normal:last-of-type",
      ".s-pagination-strip .s-pagination-item:last-of-type",
      '[data-testid="pagination"] span:last-of-type'
    ],
    
    // 下一页按钮
    NEXT_PAGE: [
      ".a-pagination .a-last a",
      ".s-pagination-strip .s-pagination-next",
      '[data-testid="pagination-next"]'
    ]
  },
  
  // JavaScript数据提取配置
  JS_DATA_PATTERNS: {
    PARENT_ASIN: /"parentAsin"\s*:\s*"([^"]+)"/,
    DIMENSION_TO_ASIN: /"dimensionToAsinMap"\s*:\s*({[^}]+})/,
    VARIATION_VALUES: /"variationValues"\s*:\s*({[^}]+})/,
    COLOR_TO_ASIN: /"colorToAsin"\s*:\s*({[^}]+})/,
    COLOR_IMAGES: /"colorImages"\s*:\s*({[\s\S]*?})\s*,\s*"heroImages"/
  }
};

// ==================== 错误配置 ====================

const ERROR_CONFIG = {
  MESSAGES: {
    INVALID_URL: "无效的Amazon产品URL",
    NO_PAGE_DATA: "无法获取页面数据，请确保在Amazon页面运行",
    NO_ASIN: "无法提取产品ASIN，可能页面结构不正确",
    SPU_SUBMIT_FAILED: "SPU数据上传失败",
    SKU_SUBMIT_FAILED: "SKU数据上传失败",
    PRODUCT_SUBMIT_FAILED: "产品详情数据上传失败",
    TASK_MARK_FAILED: "标记任务完成失败",
    NETWORK_ERROR: "网络请求失败",
    PARSE_ERROR: "数据解析失败"
  },
  
  CODES: {
    INVALID_URL: "INVALID_URL",
    NO_PAGE_DATA: "NO_PAGE_DATA",
    NO_ASIN: "NO_ASIN",
    SUBMIT_FAILED: "SUBMIT_FAILED",
    NETWORK_ERROR: "NETWORK_ERROR",
    PARSE_ERROR: "PARSE_ERROR"
  }
};

// ==================== 工具函数 ====================

/**
 * 生成唯一客户端ID
 * @returns {string} 客户端ID
 */
function generateUniqueId() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `client_${timestamp}_${random}_${SYSTEM_CONFIG.CLIENT_VERSION}`;
}

/**
 * 获取随机延迟时间
 * @param {number} min 最小延迟（毫秒）
 * @param {number} max 最大延迟（毫秒）
 * @returns {number} 随机延迟时间
 */
function getRandomDelay(min, max) {
  return Math.random() * (max - min) + min;
}

/**
 * 检查是否为Amazon域名
 * @param {string} url URL地址
 * @returns {boolean} 是否为Amazon域名
 */
function isAmazonDomain(url) {
  try {
    const hostname = new URL(url).hostname.toLowerCase();
    return AMAZON_CONFIG.DOMAINS.some(domain => 
      hostname === domain || hostname.endsWith('.' + domain)
    );
  } catch (error) {
    return false;
  }
}

/**
 * 检查是否为Amazon产品详情页
 * @param {string} url URL地址
 * @returns {boolean} 是否为产品详情页
 */
function isAmazonProductPage(url) {
  return isAmazonDomain(url) && AMAZON_CONFIG.URL_PATTERNS.PRODUCT_DETAIL.test(url);
}

/**
 * 从URL中提取ASIN
 * @param {string} url URL地址
 * @returns {string|null} ASIN或null
 */
function extractAsinFromUrl(url) {
  const match = url.match(AMAZON_CONFIG.URL_PATTERNS.PRODUCT_DETAIL);
  return match ? match[1] : null;
}

/**
 * 格式化时间戳
 * @param {Date|string} date 日期对象或ISO字符串
 * @returns {string} 格式化的时间字符串
 */
function formatTimestamp(date) {
  const d = date instanceof Date ? date : new Date(date);
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// ==================== 导出配置 ====================

// 创建配置对象
const AmazonCrawlerConfig = {
  API_ENDPOINTS,
  SYSTEM_CONFIG,
  AMAZON_CONFIG,
  ERROR_CONFIG,
  generateUniqueId,
  getRandomDelay,
  isAmazonDomain,
  isAmazonProductPage,
  extractAsinFromUrl,
  formatTimestamp
};

// ==================== Amazon详情页面调试工具 ====================

const debugAmazonCrawler = {
  // 测试采集功能
  testCrawling: function() {
    console.log("=== Amazon详情页面采集调试工具 ===");

    const results = {
      pageInfo: this.getPageInfo(),
      productData: this.extractProductData(),
      images: this.extractImages(),
      pricing: this.extractPricing(),
      availability: this.extractAvailability(),
      reviews: this.extractReviews(),
      specifications: this.extractSpecifications(),
      variations: this.extractVariations()
    };

    console.log("采集结果:", results);
    return results;
  },

  // 获取页面基本信息
  getPageInfo: function() {
    return {
      url: window.location.href,
      title: document.title,
      asin: this.extractASIN(),
      timestamp: new Date().toISOString(),
      isProductPage: this.isProductPage()
    };
  },

  // 提取ASIN
  extractASIN: function() {
    const url = window.location.href;
    const match = url.match(/\/dp\/([A-Z0-9]{10})/);
    return match ? match[1] : null;
  },

  // 提取产品数据
  extractProductData: function() {
    const data = {};

    // 产品标题
    const titleSelectors = [
      '#productTitle',
      '.product-title',
      'h1[data-automation-id="product-title"]'
    ];
    data.title = this.getTextBySelectors(titleSelectors);

    // 品牌
    const brandSelectors = [
      '#bylineInfo',
      '.a-link-normal[data-hook="product-link"]',
      '.po-brand .po-break-word'
    ];
    data.brand = this.getTextBySelectors(brandSelectors);

    // 产品描述要点
    const bulletPoints = [];
    const bullets = document.querySelectorAll('#featurebullets_feature_div li span.a-list-item');
    bullets.forEach(bullet => {
      const text = bullet.textContent.trim();
      if (text && !text.includes('Make sure') && text.length > 10) {
        bulletPoints.push(text);
      }
    });
    data.bulletPoints = bulletPoints;

    return data;
  },

  // 提取图片
  extractImages: function() {
    const images = {
      main: [],
      thumbnails: [],
      additional: []
    };

    // 主图
    const mainImageSelectors = [
      '#landingImage',
      '#imgBlkFront',
      '.a-dynamic-image'
    ];
    mainImageSelectors.forEach(selector => {
      const img = document.querySelector(selector);
      if (img && img.src) {
        images.main.push(img.src);
      }
    });

    // 缩略图
    const thumbnails = document.querySelectorAll('#altImages img, .imageThumbnail img');
    thumbnails.forEach(img => {
      if (img.src && !img.src.includes('data:image')) {
        images.thumbnails.push(img.src);
      }
    });

    return images;
  },

  // 提取价格信息
  extractPricing: function() {
    const pricing = {};

    // 当前价格
    const priceSelectors = [
      '.a-price.a-text-price.a-size-medium.apexPriceToPay .a-offscreen',
      '.a-price .a-offscreen',
      '#priceblock_dealprice',
      '#priceblock_ourprice'
    ];
    pricing.currentPrice = this.getTextBySelectors(priceSelectors);

    // 原价
    const listPriceSelectors = [
      '.a-price.a-text-price .a-offscreen',
      '#listPrice .a-offscreen',
      '.a-text-strike .a-offscreen'
    ];
    pricing.listPrice = this.getTextBySelectors(listPriceSelectors);

    return pricing;
  },

  // 提取库存信息
  extractAvailability: function() {
    const availability = {};

    const availabilitySelectors = [
      '#availability span',
      '#availabilityInsideBuyBox_feature_div span',
      '.a-color-success',
      '.a-color-state'
    ];
    availability.status = this.getTextBySelectors(availabilitySelectors);

    return availability;
  },

  // 提取评论信息
  extractReviews: function() {
    const reviews = {};

    // 评分
    const ratingSelectors = [
      '[data-hook="average-star-rating"] .a-icon-alt',
      '.a-icon-alt'
    ];
    reviews.rating = this.getTextBySelectors(ratingSelectors);

    // 评论数量
    const countSelectors = [
      '[data-hook="total-review-count"]',
      '#acrCustomerReviewText'
    ];
    reviews.count = this.getTextBySelectors(countSelectors);

    return reviews;
  },

  // 提取规格信息
  extractSpecifications: function() {
    const specs = {};

    // 产品详情表格
    const specTables = document.querySelectorAll('#productDetails_techSpec_section_1 tr, #productDetails_detailBullets_sections1 tr');
    specTables.forEach(row => {
      const cells = row.querySelectorAll('td');
      if (cells.length >= 2) {
        const key = cells[0].textContent.trim();
        const value = cells[1].textContent.trim();
        if (key && value) {
          specs[key] = value;
        }
      }
    });

    return specs;
  },

  // 提取变体信息
  extractVariations: function() {
    const variations = {};

    // 尝试从页面脚本中提取变体数据
    const scripts = document.querySelectorAll('script');
    scripts.forEach(script => {
      const content = script.textContent;
      if (content.includes('dimensionToAsinMap')) {
        try {
          const match = content.match(/"dimensionToAsinMap"\s*:\s*({[^}]+})/);
          if (match) {
            variations.dimensionToAsin = JSON.parse(match[1]);
          }
        } catch (e) {
          console.warn('解析dimensionToAsinMap失败:', e);
        }
      }

      if (content.includes('colorToAsin')) {
        try {
          const match = content.match(/"colorToAsin"\s*:\s*({[^}]+})/);
          if (match) {
            variations.colorToAsin = JSON.parse(match[1]);
          }
        } catch (e) {
          console.warn('解析colorToAsin失败:', e);
        }
      }
    });

    return variations;
  },

  // 通用文本提取方法
  getTextBySelectors: function(selectors) {
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent) {
        return element.textContent.trim();
      }
    }
    return null;
  },

  // 检查页面是否为Amazon产品页
  isProductPage: function() {
    return window.location.href.includes('/dp/') && this.extractASIN() !== null;
  },

  // 获取页面所有可用的选择器
  getAllSelectors: function() {
    const selectors = {
      titles: ['#productTitle', '.product-title', 'h1[data-automation-id="product-title"]'],
      prices: ['.a-price .a-offscreen', '#priceblock_dealprice', '#priceblock_ourprice'],
      images: ['#landingImage', '#imgBlkFront', '.a-dynamic-image'],
      availability: ['#availability span', '#availabilityInsideBuyBox_feature_div span'],
      reviews: ['[data-hook="average-star-rating"]', '#acrCustomerReviewText']
    };

    console.log("可用选择器:", selectors);

    // 测试每个选择器
    Object.keys(selectors).forEach(category => {
      console.log(`\n=== ${category} ===`);
      selectors[category].forEach(selector => {
        const element = document.querySelector(selector);
        console.log(`${selector}: ${element ? '✓ 找到' : '✗ 未找到'}`);
        if (element) {
          console.log(`  内容: ${element.textContent ? element.textContent.trim().substring(0, 100) : '无文本内容'}`);
        }
      });
    });

    return selectors;
  },

  // 测试特定选择器
  testSelector: function(selector) {
    const elements = document.querySelectorAll(selector);
    console.log(`选择器 "${selector}" 找到 ${elements.length} 个元素:`);
    elements.forEach((element, index) => {
      console.log(`  [${index}]:`, element.textContent ? element.textContent.trim().substring(0, 100) : element);
    });
    return elements;
  },

  // 获取页面所有data-asin属性
  getAllAsins: function() {
    const asinElements = document.querySelectorAll('[data-asin]');
    const asins = [];
    asinElements.forEach(element => {
      const asin = element.getAttribute('data-asin');
      if (asin && !asins.includes(asin)) {
        asins.push(asin);
      }
    });
    console.log('页面中找到的所有ASIN:', asins);
    return asins;
  }
};

// 如果在浏览器环境中，将配置添加到全局对象
if (typeof window !== 'undefined') {
  window.AmazonCrawlerConfig = AmazonCrawlerConfig;
  window.debugAmazonCrawler = debugAmazonCrawler;
}

// 如果在Service Worker环境中，将配置添加到全局作用域
if (typeof self !== 'undefined' && typeof window === 'undefined') {
  self.AmazonCrawlerConfig = AmazonCrawlerConfig;
}

// 如果在Node.js环境中，使用module.exports
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AmazonCrawlerConfig;
}
