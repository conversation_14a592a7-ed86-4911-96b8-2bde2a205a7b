/**
 * Amazon采集插件调试助手
 * 用于诊断目录采集功能的问题
 */

// 调试助手类
class AmazonCrawlerDebugger {
  constructor() {
    this.logs = [];
    this.startTime = new Date();
    this.checkInterval = null;
  }

  /**
   * 记录调试信息
   */
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      type,
      message,
      timeFromStart: Date.now() - this.startTime.getTime()
    };
    
    this.logs.push(logEntry);
    
    const prefix = `[DEBUG ${type.toUpperCase()}]`;
    switch (type) {
      case 'error':
        console.error(prefix, message);
        break;
      case 'warn':
        console.warn(prefix, message);
        break;
      case 'success':
        console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
        break;
      default:
        console.log(prefix, message);
    }
  }

  /**
   * 检查扩展基础状态
   */
  async checkExtensionStatus() {
    this.log('开始检查扩展基础状态...');
    
    // 检查jQuery
    if (typeof $ !== 'undefined') {
      this.log('✅ jQuery已加载', 'success');
    } else {
      this.log('❌ jQuery未加载', 'error');
    }
    
    // 检查配置文件
    if (typeof window.AmazonCrawlerConfig !== 'undefined') {
      this.log('✅ 配置文件已加载', 'success');
      this.log(`配置内容: ${Object.keys(window.AmazonCrawlerConfig).join(', ')}`);
    } else {
      this.log('❌ 配置文件未加载', 'error');
    }
    
    // 检查Amazon解析器
    if (typeof window.AmazonParser !== 'undefined') {
      this.log('✅ Amazon解析器已加载', 'success');
    } else {
      this.log('❌ Amazon解析器未加载', 'error');
    }
    
    // 检查关键函数
    const keyFunctions = [
      'amazon_list_caiji',
      'getPendingListTasks',
      'extractAmazonSearchResults',
      'uploadListTaskResult',
      'isAmazonSearchPage'
    ];
    
    keyFunctions.forEach(funcName => {
      if (typeof window[funcName] === 'function') {
        this.log(`✅ 函数 ${funcName} 已定义`, 'success');
      } else {
        this.log(`❌ 函数 ${funcName} 未定义`, 'error');
      }
    });
  }

  /**
   * 检查页面状态
   */
  checkPageStatus() {
    this.log('检查页面状态...');
    
    const url = window.location.href;
    this.log(`当前URL: ${url}`);
    
    // 检查是否为Amazon页面
    if (url.includes('amazon.com')) {
      this.log('✅ 当前在Amazon页面', 'success');
      
      // 检查是否为搜索页面
      if (typeof isAmazonSearchPage === 'function') {
        if (isAmazonSearchPage(url)) {
          this.log('✅ 当前是Amazon搜索页面', 'success');
          
          // 检查搜索结果容器
          const containers = [
            'span[data-component-type="s-search-results"]',
            '[data-component-type="s-search-results"]',
            '.s-search-results'
          ];
          
          let foundContainer = false;
          containers.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
              this.log(`✅ 找到搜索结果容器: ${selector}`, 'success');
              foundContainer = true;
            }
          });
          
          if (!foundContainer) {
            this.log('❌ 未找到搜索结果容器', 'error');
          }
        } else {
          this.log('⚠️ 当前不是Amazon搜索页面', 'warn');
        }
      }
    } else {
      this.log('⚠️ 当前不在Amazon页面', 'warn');
    }
  }

  /**
   * 检查Chrome扩展API
   */
  checkChromeAPIs() {
    this.log('检查Chrome扩展API...');
    
    if (typeof chrome !== 'undefined') {
      this.log('✅ Chrome API可用', 'success');
      
      if (chrome.runtime) {
        this.log('✅ chrome.runtime可用', 'success');
      } else {
        this.log('❌ chrome.runtime不可用', 'error');
      }
      
      if (chrome.storage) {
        this.log('✅ chrome.storage可用', 'success');
      } else {
        this.log('❌ chrome.storage不可用', 'error');
      }
    } else {
      this.log('❌ Chrome API不可用', 'error');
    }
  }

  /**
   * 测试与background的通信
   */
  async testBackgroundCommunication() {
    this.log('测试与background的通信...');
    
    return new Promise((resolve) => {
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        this.log('❌ Chrome runtime不可用，无法测试通信', 'error');
        resolve(false);
        return;
      }
      
      chrome.runtime.sendMessage({
        action: "can_zidong_caiji",
        info: "debug_test"
      }, (response) => {
        if (chrome.runtime.lastError) {
          this.log(`❌ Background通信失败: ${chrome.runtime.lastError.message}`, 'error');
          resolve(false);
        } else {
          this.log('✅ Background通信成功', 'success');
          this.log(`Background响应: ${JSON.stringify(response)}`);
          resolve(true);
        }
      });
      
      // 5秒超时
      setTimeout(() => {
        this.log('⚠️ Background通信超时', 'warn');
        resolve(false);
      }, 5000);
    });
  }

  /**
   * 检查本地存储
   */
  async checkLocalStorage() {
    this.log('检查本地存储...');
    
    if (typeof chrome === 'undefined' || !chrome.storage) {
      this.log('❌ Chrome storage不可用', 'error');
      return;
    }
    
    try {
      const result = await new Promise((resolve, reject) => {
        chrome.storage.local.get(['listTask', 'uniqueId', 'auto_start_detail_crawling'], (result) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(result);
          }
        });
      });
      
      this.log('✅ 本地存储访问成功', 'success');
      
      if (result.listTask) {
        this.log(`当前列表任务: ${JSON.stringify(result.listTask)}`);
      } else {
        this.log('无当前列表任务');
      }
      
      if (result.uniqueId) {
        this.log(`客户端ID: ${result.uniqueId}`);
      } else {
        this.log('无客户端ID');
      }
      
      if (result.auto_start_detail_crawling) {
        this.log(`自动启动详情采集: ${result.auto_start_detail_crawling}`);
      }
      
    } catch (error) {
      this.log(`❌ 本地存储访问失败: ${error.message}`, 'error');
    }
  }

  /**
   * 手动触发采集测试
   */
  async testManualCrawling() {
    this.log('手动触发采集测试...');

    if (typeof amazon_list_caiji === 'function') {
      try {
        this.log('调用 amazon_list_caiji()...');
        await amazon_list_caiji();
        this.log('✅ amazon_list_caiji() 调用完成', 'success');
      } catch (error) {
        this.log(`❌ amazon_list_caiji() 调用失败: ${error.message}`, 'error');
      }
    } else {
      this.log('❌ amazon_list_caiji 函数不存在', 'error');
    }
  }

  /**
   * 测试Gzip压缩功能
   */
  async testGzipCompression() {
    this.log('测试Gzip压缩功能...');

    if (typeof window.gzipUtils === 'undefined') {
      this.log('❌ Gzip工具未加载', 'error');
      return;
    }

    // 测试数据
    const testData = `
      <div data-component-type="s-search-results">
        <div class="s-result-item" data-asin="B08N5WRWNW">
          <h3 class="s-size-mini s-spacing-none s-color-base">
            <a href="/dp/B08N5WRWNW">Apple MacBook Air</a>
          </h3>
          <span class="a-price-whole">999</span>
          <span class="a-price-fraction">00</span>
        </div>
      </div>
    `.repeat(20); // 重复20次增加数据量

    try {
      const result = await window.gzipUtils.compressToBase64(testData);

      this.log('✅ Gzip压缩测试成功', 'success');
      this.log(`原始大小: ${result.originalSize} bytes`);
      this.log(`压缩后大小: ${result.compressedSize} bytes`);
      this.log(`压缩比: ${result.ratio.toFixed(2)}%`);
      this.log(`压缩方法: ${result.method}`);
      this.log(`是否压缩: ${result.isCompressed ? '是' : '否'}`);

      return result;
    } catch (error) {
      this.log(`❌ Gzip压缩测试失败: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * 测试当前页面HTML提取和压缩
   */
  async testCurrentPageCompression() {
    this.log('测试当前页面HTML提取和压缩...');

    // 查找搜索结果容器
    const containers = [
      'span[data-component-type="s-search-results"]',
      '[data-component-type="s-search-results"]',
      '.s-search-results'
    ];

    let searchResultsElement = null;
    for (const selector of containers) {
      searchResultsElement = document.querySelector(selector);
      if (searchResultsElement) {
        this.log(`✅ 找到搜索结果容器: ${selector}`, 'success');
        break;
      }
    }

    if (!searchResultsElement) {
      this.log('❌ 未找到搜索结果容器', 'error');
      return null;
    }

    const originalHtml = searchResultsElement.outerHTML;
    this.log(`原始HTML大小: ${originalHtml.length} 字符`);

    // 清理HTML
    let cleanedHtml = originalHtml;
    if (typeof cleanSearchResultsHtml === 'function') {
      cleanedHtml = cleanSearchResultsHtml(originalHtml);
      this.log(`清理后HTML大小: ${cleanedHtml.length} 字符`);
    }

    // 压缩测试
    if (typeof window.gzipUtils !== 'undefined') {
      try {
        const result = await window.gzipUtils.compressToBase64(cleanedHtml);

        this.log('✅ 当前页面压缩测试成功', 'success');
        this.log(`清理前: ${originalHtml.length} 字符`);
        this.log(`清理后: ${cleanedHtml.length} 字符`);
        this.log(`压缩后: ${result.compressedSize} bytes`);
        this.log(`总压缩比: ${((originalHtml.length - result.compressedSize) / originalHtml.length * 100).toFixed(2)}%`);

        return {
          originalSize: originalHtml.length,
          cleanedSize: cleanedHtml.length,
          compressedSize: result.compressedSize,
          compressionResult: result
        };
      } catch (error) {
        this.log(`❌ 压缩失败: ${error.message}`, 'error');
      }
    }

    return null;
  }

  /**
   * 测试SPU和变体数据提取
   */
  async testSpuSkuExtraction() {
    this.log('测试SPU和变体数据提取...');

    if (typeof extractAmazonSearchResults === 'function') {
      try {
        const results = extractAmazonSearchResults();
        this.log(`✅ 提取到 ${results.length} 个商品`, 'success');

        results.forEach((item, index) => {
          this.log(`商品 ${index + 1}: ${item.title || 'N/A'} - ${item.asin || 'N/A'}`);
        });

        return results;
      } catch (error) {
        this.log(`❌ SPU/SKU提取失败: ${error.message}`, 'error');
      }
    } else {
      this.log('❌ extractAmazonSearchResults 函数不存在', 'error');
    }

    return null;
  }

  /**
   * 运行完整诊断
   */
  async runFullDiagnosis() {
    this.log('=== 开始完整诊断 ===', 'success');
    
    await this.checkExtensionStatus();
    this.checkPageStatus();
    this.checkChromeAPIs();
    await this.checkLocalStorage();
    await this.testBackgroundCommunication();
    
    this.log('=== 诊断完成 ===', 'success');
    this.log(`总共记录了 ${this.logs.length} 条日志`);
    
    return this.logs;
  }

  /**
   * 导出诊断报告
   */
  exportReport() {
    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      logs: this.logs
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `amazon-crawler-debug-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    this.log('✅ 诊断报告已导出', 'success');
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    this.log('开始监控采集状态...');
    
    this.checkInterval = setInterval(() => {
      // 检查是否有新的控制台错误
      // 这里可以添加更多监控逻辑
    }, 10000);
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      this.log('监控已停止');
    }
  }
}

// 创建全局调试器实例
window.AmazonCrawlerDebugger = new AmazonCrawlerDebugger();

// 添加控制台快捷命令
window.debugAmazonCrawler = {
  // 运行完整诊断
  diagnose: () => window.AmazonCrawlerDebugger.runFullDiagnosis(),

  // 测试手动采集
  testCrawling: () => window.AmazonCrawlerDebugger.testManualCrawling(),

  // 检查扩展状态
  checkExtension: () => window.AmazonCrawlerDebugger.checkExtensionStatus(),

  // 测试Background通信
  testBackground: () => window.AmazonCrawlerDebugger.testBackgroundCommunication(),

  // 测试Gzip压缩
  testGzip: () => window.AmazonCrawlerDebugger.testGzipCompression(),

  // 测试当前页面压缩
  testPageCompression: () => window.AmazonCrawlerDebugger.testCurrentPageCompression(),

  // 测试SPU/SKU提取
  testSpuSku: () => window.AmazonCrawlerDebugger.testSpuSkuExtraction(),

  // 导出报告
  exportReport: () => window.AmazonCrawlerDebugger.exportReport(),

  // 开始监控
  startMonitor: () => window.AmazonCrawlerDebugger.startMonitoring(),

  // 停止监控
  stopMonitor: () => window.AmazonCrawlerDebugger.stopMonitoring()
};

console.log('🔧 Amazon采集调试助手已加载！');
console.log('📋 可用命令:');
console.log('  debugAmazonCrawler.diagnose() - 运行完整诊断');
console.log('  debugAmazonCrawler.testCrawling() - 测试手动采集');
console.log('  debugAmazonCrawler.testGzip() - 测试Gzip压缩');
console.log('  debugAmazonCrawler.testPageCompression() - 测试当前页面压缩');
console.log('  debugAmazonCrawler.testSpuSku() - 测试SPU/SKU提取');
console.log('  debugAmazonCrawler.exportReport() - 导出诊断报告');
