package com.xinghuo.amazon.collect.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 亚马逊产品SPU（父体）信息实体类
 */
@Data
@TableName("zz_amazon_products_spu")
public class AmazonProductSpuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "asin", type = IdType.INPUT)
    private String asin;

    @TableField("title")
    private String title;

    @TableField("brand")
    private String brand;

    @TableField("rating")
    private BigDecimal rating;

    @TableField("review_count")
    private Integer reviewCount;

    @TableField("main_image_url")
    private String mainImageUrl;

    @TableField("image_urls")
    private String imageUrls; // JSON数组，建议保留为字符串

    @TableField("bullet_points")
    private String bulletPoints; // JSON数组，五点描述

    @TableField("description")
    private String description;

    @TableField("product_details")
    private String productDetails; // JSON格式的参数表格

    @TableField("category_path")
    private String categoryPath;

    @TableField(value = "first_crawled_at", fill = FieldFill.INSERT)
    private Date firstCrawledAt;

    @TableField(value = "last_crawled_at", fill = FieldFill.INSERT_UPDATE)
    private Date lastCrawledAt;

    @TableField("product_attributes")
    private String productAttributes; // JSON格式，包含技术规格

    @TableField("temu_product_detail")
    private String temuProductDetail; // 兼容字段，保留为字符串
}
