/**
 * Gzip压缩工具类
 * 支持浏览器原生CompressionStream API和pako库后备方案
 */

class GzipUtils {
  constructor() {
    this.isPakoLoaded = false;

    // 检查pako是否已加载
    if (typeof pako !== 'undefined') {
      this.isPakoLoaded = true;
      console.log('Pako库已加载，版本:', pako.version || 'unknown');
    } else {
      console.warn('Pako库未加载，压缩功能将不可用');
    }
  }





  /**
   * 使用pako库压缩数据
   */
  compressWithPako(data) {
    try {
      console.log('开始pako压缩，数据大小:', data.length);

      if (typeof pako !== 'undefined') {
        // 使用pako库进行gzip压缩
        const compressed = pako.gzip(data, { level: 6 }); // 使用中等压缩级别
        console.log('pako压缩完成，压缩后大小:', compressed.length);
        return compressed;
      } else {
        throw new Error('pako库未加载');
      }
    } catch (error) {
      console.error('Pako压缩失败:', error);
      throw error;
    }
  }

  /**
   * 压缩字符串数据
   * @param {string} data 要压缩的字符串
   * @returns {Promise<{compressed: Uint8Array, originalSize: number, compressedSize: number, ratio: number}>}
   */
  async compress(data) {
    const originalSize = new TextEncoder().encode(data).length;
    let compressed;
    let method = '';

    try {
      // 只使用pako库进行压缩
      if (this.isPakoLoaded && typeof pako !== 'undefined') {
        console.log('使用pako库进行压缩');
        compressed = this.compressWithPako(data);
        method = 'pako';
      } else {
        throw new Error('pako库未加载，无法压缩');
      }

      const compressedSize = compressed.length;
      const ratio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);

      console.log(`Gzip压缩完成 (${method}):`, {
        originalSize,
        compressedSize,
        ratio: `${ratio}%`
      });

      return {
        compressed,
        originalSize,
        compressedSize,
        ratio: parseFloat(ratio),
        method
      };
    } catch (error) {
      console.error('压缩失败，返回原始数据:', error);
      // 如果压缩失败，返回原始数据
      const encoder = new TextEncoder();
      return {
        compressed: encoder.encode(data),
        originalSize,
        compressedSize: originalSize,
        ratio: 0,
        method: 'none'
      };
    }
  }

  /**
   * 将Uint8Array转换为Base64字符串
   */
  arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    
    return btoa(binary);
  }

  /**
   * 压缩并转换为Base64
   * @param {string} data 要压缩的字符串
   * @returns {Promise<{data: string, isCompressed: boolean, originalSize: number, compressedSize: number, ratio: number}>}
   */
  async compressToBase64(data) {
    const result = await this.compress(data);
    
    return {
      data: this.arrayBufferToBase64(result.compressed),
      isCompressed: result.method !== 'none',
      originalSize: result.originalSize,
      compressedSize: result.compressedSize,
      ratio: result.ratio,
      method: result.method
    };
  }
}

// 创建全局实例
window.gzipUtils = new GzipUtils();

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GzipUtils;
}
