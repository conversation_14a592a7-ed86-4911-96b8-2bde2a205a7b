*{
  box-sizing: border-box;
}
body {
  width: 200px;
  height: 200px;
  box-sizing: border-box;
  font-family: 'Arial', sans-serif;
  background-color: #f9f9f9;
}
.footer-wrapper{
  height: 20px;
}

.header-top-wrapper{
  display: flex;
  justify-content: space-between;
  height: 45px;
}
.logo-wrapper{
  font-style: normal;
  font-size: 16px;
  text-align: left;
  margin-top: -5px;
  display: flex;
}
.logo-wrapper img{
  width: 65px;
  height: auto;
  vertical-align: middle;
}
.logo-wrapper .logo-desc{
  padding-top: 23px;
  margin-left: -3px;
}
.header-top-wrapper p{
  margin:0;
  padding:0;
  padding-top: 21px;
  padding-right: 10px;
}
.header-nav-box{
  display: flex;
  justify-content: space-around;
  background-color: #ff5722;
}
.header-nav-box .nav-item{
  padding: 9px 0;
  color: #FFF;
  font-size: 14px;
  line-height: 12px;
  padding-top: 15px;
  font-weight: 500;
  border-bottom: 2px solid #ff5722;
}
.header-nav-box .nav-item.active{
  border-bottom: 2px solid #FFF;
}

.content-wrapper{

}
.content-wrapper .tab-item{
  display: none;
}
.content-wrapper .tab-item.active{
  display: block;
}

.footer-wrapper{
  text-align: center;
}




.hello {
  font-size: 14px;
  margin-bottom: 4px;
  font-weight: 500;
}

.logList {
  width: 400px;
  height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding: 6px 20px;
  border: 1px solid #666;
  border-radius: 8px;
  font-size: 16px;
  color: #444;
}

.title {
  width: 100%;
  display: flex;
  justify-content: center;
  font-weight: bold;
}

.log {
  display: flex;
  width: 100%;
  flex-direction: row;
  align-items: center;
}

.log > .dot{
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #333;
  margin-right: 6px;
 }

.log > p {
  display: flex;
  flex: 1;
  align-items: center;
}

.log > .btn {
  display: flex;
  width: 30px;
  justify-content: center;
  align-items: center;
}

.log > .btn > button {
  cursor: pointer;
}
/**/
#message-info{
  margin:5px 0;
}
#message-info .alert-info{
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
    padding:5px;
}
.field-row .field-label{
  margin:5px 0;
}

.batchdig-list-wrapper{
  max-height: 250px;
  overflow-y: auto;
  margin:10px 0;
}
.batchdig-list-wrapper .bl-item-emails{
  margin-left: 10px;
}
/**/
form input[type='button']{
  margin-bottom: 10px;
}

.nav-box{
  display: flex;
}
.nav-box .nav-item{
  padding: 5px 0;
  cursor: pointer;
  font-size: 16px;
}
.nav-box .nav-item:first-child{
  margin-right: 10px;
}
.nav-box .nav-item.active{
  font-weight: bold;
}
/**/
.snav-box{
  display: flex;
  margin-bottom: 10px;
}
.snav-box .snav-item{
  padding: 5px 0;
  cursor: pointer;
}
.snav-box .snav-item:first-child{
  margin-right: 10px;
}
.snav-box .snav-item.active{
  font-weight: bold;
}
/**/
form label{
  font-weight: normal;
}




a {
  color: #007bff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 按钮样式 */
button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  background-color: #ddddf5;
  color: black;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #d98be4;
}



/* 输入框和下拉菜单 */
input[type="text"], select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  margin-bottom: 10px;
  width: calc(100% - 18px); /* Adjusts for padding and border */
}

input[type="text"]:focus, select:focus {
  outline: none;
  border-color: black;
}

/* 悬浮提示 */
[data-tip]:after {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 0.75em;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  top: auto;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 5px;
}

[data-tip]:before {
  border-bottom: none;
  border-top: 5px solid rgba(0, 0, 0, 0.8);
  top: auto;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
}


/* 响应式设计 */
@media (max-width: 768px) {
  .header-top-wrapper, .nav-box, .snav-box {
    flex-direction: column;
    align-items: center;
  }
}