<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background.js 解析器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007185;
        }
        .test-controls {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .test-file {
            margin: 10px 0;
        }
        .test-file button {
            background: #007185;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-file button:hover {
            background: #005a6b;
        }
        .test-file button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
        }
        .result-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .result-section h3 {
            margin-top: 0;
            color: #007185;
        }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-left: 4px solid #007185;
        }
        .result-item strong {
            color: #333;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            border-left-color: #d32f2f;
        }
        .success {
            color: #2e7d32;
            background: #e8f5e8;
            border-left-color: #2e7d32;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
            border-left-color: #f57c00;
        }
        .json-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background: #007185;
            color: white;
        }
        .comparison-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .progress {
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #007185;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Background.js Amazon解析器测试工具</h1>
            <p>测试background.js中基于HTML字符串的Amazon产品解析功能</p>
        </div>

        <div class="test-controls">
            <h2>📁 测试文件</h2>
            <div class="test-file">
                <button id="btn-m2">测试 detail.m2.html</button>
                <span>大型产品详情页 (~19K行)</span>
            </div>
            <div class="test-file">
                <button id="btn-multi">测试 detail.multi.html</button>
                <span>多变体产品页 (~12K行)</span>
            </div>
            <div class="test-file">
                <button id="btn-detail1">测试 detail1.html</button>
                <span>标准产品页 (~12K行)</span>
            </div>
            <div class="test-file">
                <button id="btn-all">🚀 测试所有文件</button>
                <span>批量测试并比较结果</span>
            </div>
            <div class="test-file">
                <button id="btn-clear">🗑️ 清空结果</button>
            </div>
        </div>

        <div class="progress" id="progress" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
            </div>
            <div id="progress-text">准备中...</div>
        </div>

        <div class="results" id="results">
            <div class="loading">
                <p>👆 点击上方按钮开始测试</p>
                <p>测试将验证background.js中的解析函数是否能正确提取Amazon产品数据</p>
            </div>
        </div>
    </div>

    <script>
        // 测试状态
        let testResults = {};
        let currentTest = null;

        // 从background.js复制的解析函数
        function ct() {
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var day = now.getDate();
            var hours = now.getHours();
            var minutes = now.getMinutes();
            var seconds = now.getSeconds();

            function pad(number) {
                return (number < 10 ? "0" : "") + number;
            }

            return year + "-" + pad(month) + "-" + pad(day) + " " + 
                   pad(hours) + ":" + pad(minutes) + ":" + pad(seconds) + " ";
        }

        // 工具函数：显示进度
        function showProgress(show, text = '', percent = 0) {
            const progressDiv = document.getElementById('progress');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            
            if (show) {
                progressDiv.style.display = 'block';
                progressFill.style.width = percent + '%';
                progressText.textContent = text;
            } else {
                progressDiv.style.display = 'none';
            }
        }

        // 工具函数：禁用/启用按钮
        function setButtonsEnabled(enabled) {
            const buttons = ['btn-m2', 'btn-multi', 'btn-detail1', 'btn-all', 'btn-clear'];
            buttons.forEach(id => {
                document.getElementById(id).disabled = !enabled;
            });
        }

        // 加载HTML文件
        async function loadHtmlFile(filePath) {
            try {
                const response = await fetch(filePath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const html = await response.text();
                console.log(ct(), `文件加载成功: ${filePath}, 大小: ${html.length} 字符`);
                return html;
            } catch (error) {
                console.error(ct(), `文件加载失败: ${filePath}`, error);
                throw error;
            }
        }

        // 清空结果
        function clearResults() {
            testResults = {};
            document.getElementById('results').innerHTML = `
                <div class="loading">
                    <p>👆 点击上方按钮开始测试</p>
                    <p>测试将验证background.js中的解析函数是否能正确提取Amazon产品数据</p>
                </div>
            `;
        }

        // 显示错误信息
        function showError(message, error) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-section">
                    <h3>❌ 测试失败</h3>
                    <div class="result-item error">
                        <strong>错误信息:</strong> ${message}<br>
                        <strong>详细错误:</strong> ${error.message || error}
                    </div>
                </div>
            `;
        }

        // ==================== 从background.js复制的解析函数 ====================

        /**
         * 提取ASIN
         */
        function extractAsinFromHtml(html, pageUrl, taskData) {
            // 优先使用任务中的entryAsin
            if (taskData && taskData.entryAsin) {
                console.log(ct(), "使用任务entryAsin作为SPU主键:", taskData.entryAsin);
                return taskData.entryAsin;
            }

            // 尝试从JavaScript中提取parentAsin
            const parentMatch = html.match(/"parentAsin"\s*:\s*"([^"]+)"/);
            if (parentMatch) {
                console.log(ct(), "使用parentAsin作为SPU主键:", parentMatch[1]);
                return parentMatch[1];
            }

            // 从页面元素中提取data-asin
            const dataAsinMatch = html.match(/data-asin="([A-Z0-9]{10})"/);
            if (dataAsinMatch) {
                console.log(ct(), "使用data-asin作为SPU主键:", dataAsinMatch[1]);
                return dataAsinMatch[1];
            }

            // 从URL中提取ASIN
            const urlMatch = pageUrl.match(/\/dp\/([A-Z0-9]{10})/);
            const asin = urlMatch ? urlMatch[1] : 'UNKNOWN';
            console.log(ct(), "使用URL中的ASIN作为SPU主键:", asin);
            return asin;
        }

        /**
         * 提取产品标题
         */
        function extractTitleFromHtml(html) {
            const titleMatch = html.match(/<span[^>]*id="productTitle"[^>]*>(.*?)<\/span>/s);
            return titleMatch ? titleMatch[1].trim().replace(/<[^>]*>/g, '') : null;
        }

        /**
         * 提取品牌信息
         */
        function extractBrandFromHtml(html) {
            const brandMatch = html.match(/<a[^>]*id="bylineInfo"[^>]*>(.*?)<\/a>/s);
            if (brandMatch) {
                const brandText = brandMatch[1].replace(/<[^>]*>/g, '').trim();
                const storeMatch = brandText.match(/Visit the (.+?) Store/);
                return storeMatch ? storeMatch[1] : brandText;
            }
            return null;
        }

        /**
         * 提取评分
         */
        function extractRatingFromHtml(html) {
            const ratingMatch = html.match(/<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([^<]*out of[^<]*)<\/span>/);
            if (ratingMatch) {
                const ratingText = ratingMatch[1];
                const numberMatch = ratingText.match(/(\d+\.?\d*)/);
                return numberMatch ? parseFloat(numberMatch[1]) : null;
            }
            return null;
        }

        /**
         * 提取评价数量
         */
        function extractReviewCountFromHtml(html) {
            const reviewMatch = html.match(/<span[^>]*id="acrCustomerReviewText"[^>]*>([^<]*)<\/span>/);
            if (reviewMatch) {
                const reviewText = reviewMatch[1].replace(/,/g, '');
                const numberMatch = reviewText.match(/(\d+)/);
                return numberMatch ? parseInt(numberMatch[1]) : null;
            }
            return null;
        }

        /**
         * 提取主图URL
         */
        function extractMainImageFromHtml(html) {
            // 尝试多种模式匹配主图
            const patterns = [
                /<img[^>]*id="landingImage"[^>]*src="([^"]*)"[^>]*>/,
                /<img[^>]*id="imgBlkFront"[^>]*src="([^"]*)"[^>]*>/,
                /<img[^>]*data-old-hires="([^"]*)"[^>]*>/
            ];

            for (const pattern of patterns) {
                const match = html.match(pattern);
                if (match) {
                    return match[1];
                }
            }
            return null;
        }

        /**
         * 提取价格
         */
        function extractPriceFromHtml(html) {
            const pricePatterns = [
                /<span[^>]*class="[^"]*a-price[^"]*a-offscreen[^"]*"[^>]*>([^<]*)<\/span>/,
                /<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$([^<]*)<\/span>/
            ];

            for (const pattern of pricePatterns) {
                const match = html.match(pattern);
                if (match) {
                    const priceText = match[1].replace('$', '').replace(',', '');
                    const price = parseFloat(priceText);
                    if (!isNaN(price)) {
                        return price;
                    }
                }
            }
            return null;
        }

        /**
         * 提取库存状态
         */
        function extractStockStatusFromHtml(html) {
            const availabilityPatterns = [
                /<div[^>]*id="availability"[^>]*>(.*?)<\/div>/s,
                /<div[^>]*id="availabilityInsideBuyBox_feature_div"[^>]*>(.*?)<\/div>/s
            ];

            for (const pattern of availabilityPatterns) {
                const match = html.match(pattern);
                if (match) {
                    const text = match[1].replace(/<[^>]*>/g, '').trim().toLowerCase();
                    if (text.includes('in stock')) return 'In Stock';
                    if (text.includes('out of stock')) return 'Out of Stock';
                    if (text.includes('temporarily unavailable')) return 'Temporarily Unavailable';
                }
            }

            return 'Unknown';
        }

        /**
         * 从HTML字符串中提取变体数据
         */
        function extractVariationDataFromHtml(html) {
            console.log(ct(), "开始提取变体数据...");

            const variationData = {};

            // 使用正则表达式提取script标签内容
            const scriptPattern = /<script[^>]*>([\s\S]*?)<\/script>/gi;
            let scriptMatch;

            while ((scriptMatch = scriptPattern.exec(html)) !== null) {
                const scriptContent = scriptMatch[1];
                if (!scriptContent || !scriptContent.includes('dimensionToAsinMap')) {
                    continue;
                }

                try {
                    // 提取dimensionToAsinMap
                    const dimensionMatch = scriptContent.match(/"dimensionToAsinMap"\s*:\s*({[^}]+})/);
                    if (dimensionMatch) {
                        variationData.dimensionToAsinMap = JSON.parse(dimensionMatch[1]);
                        console.log(ct(), "找到dimensionToAsinMap:", variationData.dimensionToAsinMap);
                    }

                    // 提取variationValues
                    const variationMatch = scriptContent.match(/"variationValues"\s*:\s*({[^}]+})/);
                    if (variationMatch) {
                        variationData.variationValues = JSON.parse(variationMatch[1]);
                        console.log(ct(), "找到variationValues:", variationData.variationValues);
                    }

                    // 提取colorToAsin
                    const colorToAsinMatch = scriptContent.match(/"colorToAsin"\s*:\s*({[^}]+})/);
                    if (colorToAsinMatch) {
                        variationData.colorToAsin = JSON.parse(colorToAsinMatch[1]);
                        console.log(ct(), "找到colorToAsin:", variationData.colorToAsin);
                    }

                    // 提取parentAsin
                    const parentMatch = scriptContent.match(/"parentAsin"\s*:\s*"([^"]+)"/);
                    if (parentMatch) {
                        variationData.parentAsin = parentMatch[1];
                        console.log(ct(), "找到parentAsin:", parentMatch[1]);
                    }

                    break; // 找到数据后退出循环
                } catch (error) {
                    console.warn(ct(), "解析变体数据时出错:", error);
                    continue;
                }
            }

            return variationData;
        }

        /**
         * 判断是否为多变体产品
         */
        function isMultiVariantProduct(variationData) {
            const hasMultipleDimensions = variationData.dimensionToAsinMap &&
                                        Object.keys(variationData.dimensionToAsinMap).length > 1;
            const hasMultipleColors = variationData.colorToAsin &&
                                     Object.keys(variationData.colorToAsin).length > 1;

            return hasMultipleDimensions || hasMultipleColors;
        }

        /**
         * 从变体数据中提取所有SKU的ASIN
         */
        function extractSkuAsinsFromVariations(variationData) {
            const skuAsins = [];

            if (variationData.dimensionToAsinMap) {
                for (const asin of Object.values(variationData.dimensionToAsinMap)) {
                    if (asin && !skuAsins.includes(asin)) {
                        skuAsins.push(asin);
                    }
                }
            }

            if (variationData.colorToAsin) {
                for (const asin of Object.values(variationData.colorToAsin)) {
                    if (asin && !skuAsins.includes(asin)) {
                        skuAsins.push(asin);
                    }
                }
            }

            return skuAsins;
        }

        /**
         * 从HTML字符串中提取SPU数据
         */
        function extractSpuDataFromHtml(html, pageUrl = '', taskData = null) {
            console.log(ct(), "开始提取SPU数据...");

            const spuData = {};

            // 1. 提取ASIN
            spuData.asin = extractAsinFromHtml(html, pageUrl, taskData);

            // 2. 提取基础信息
            spuData.title = extractTitleFromHtml(html);
            spuData.brand = extractBrandFromHtml(html);
            spuData.rating = extractRatingFromHtml(html);
            spuData.reviewCount = extractReviewCountFromHtml(html);

            // 3. 提取图片信息
            spuData.mainImageUrl = extractMainImageFromHtml(html);

            // 4. 提取价格和库存
            spuData.price = extractPriceFromHtml(html);
            spuData.stockStatus = extractStockStatusFromHtml(html);

            console.log(ct(), "SPU数据提取完成:", spuData.asin, "-", spuData.title?.substring(0, 50) + "...");
            return spuData;
        }

        // ==================== 测试函数 ====================

        /**
         * 测试单个文件
         */
        async function testFile(filePath) {
            currentTest = filePath;
            setButtonsEnabled(false);
            showProgress(true, `正在加载 ${filePath}...`, 10);

            try {
                // 1. 加载HTML文件
                const html = await loadHtmlFile(filePath);
                showProgress(true, `正在解析 ${filePath}...`, 30);

                // 2. 模拟任务数据
                const taskData = {
                    entryAsin: null, // 让解析器自动提取
                    listPageTitle: "测试产品",
                    url: `https://www.amazon.com/dp/TEST123`
                };

                // 3. 提取SPU数据
                showProgress(true, '正在提取SPU数据...', 50);
                const spuData = extractSpuDataFromHtml(html, taskData.url, taskData);

                // 4. 提取变体数据
                showProgress(true, '正在提取变体数据...', 70);
                const variationData = extractVariationDataFromHtml(html);

                // 5. 判断产品类型
                const isMultiVariant = isMultiVariantProduct(variationData);
                const skuAsins = extractSkuAsinsFromVariations(variationData);

                showProgress(true, '正在生成测试报告...', 90);

                // 6. 保存测试结果
                testResults[filePath] = {
                    fileName: filePath.split('/').pop(),
                    htmlSize: html.length,
                    spuData: spuData,
                    variationData: variationData,
                    isMultiVariant: isMultiVariant,
                    skuAsins: skuAsins,
                    testTime: new Date().toLocaleString(),
                    success: true
                };

                // 7. 显示结果
                showProgress(true, '测试完成！', 100);
                setTimeout(() => {
                    showProgress(false);
                    displaySingleTestResult(filePath);
                    setButtonsEnabled(true);
                }, 500);

            } catch (error) {
                console.error(ct(), `测试失败: ${filePath}`, error);
                showProgress(false);
                showError(`测试文件 ${filePath} 失败`, error);
                setButtonsEnabled(true);
            }
        }

        /**
         * 测试所有文件
         */
        async function testAllFiles() {
            const files = ['doc/detail.m2.html', 'doc/detail.multi.html', 'doc/detail1.html'];
            setButtonsEnabled(false);

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const progress = ((i + 1) / files.length) * 100;

                showProgress(true, `正在测试 ${file} (${i + 1}/${files.length})...`, progress);

                try {
                    await testFile(file);
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
                } catch (error) {
                    console.error(ct(), `批量测试失败: ${file}`, error);
                }
            }

            showProgress(false);
            displayComparisonResults();
            setButtonsEnabled(true);
        }

        /**
         * 显示单个测试结果
         */
        function displaySingleTestResult(filePath) {
            const result = testResults[filePath];
            if (!result) return;

            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-section">
                    <h3>📊 测试结果: ${result.fileName}</h3>
                    <div class="result-item success">
                        <strong>测试时间:</strong> ${result.testTime}<br>
                        <strong>文件大小:</strong> ${result.htmlSize.toLocaleString()} 字符<br>
                        <strong>产品类型:</strong> ${result.isMultiVariant ? '多变体产品' : '单变体产品'}
                        ${result.isMultiVariant ? `<br><strong>SKU数量:</strong> ${result.skuAsins.length}` : ''}
                    </div>
                </div>

                <div class="result-section">
                    <h3>🏷️ SPU数据</h3>
                    <div class="result-item ${result.spuData.asin !== 'UNKNOWN' ? 'success' : 'warning'}">
                        <strong>ASIN:</strong> ${result.spuData.asin || '未提取到'}<br>
                        <strong>标题:</strong> ${result.spuData.title || '未提取到'}<br>
                        <strong>品牌:</strong> ${result.spuData.brand || '未提取到'}<br>
                        <strong>评分:</strong> ${result.spuData.rating || '未提取到'}<br>
                        <strong>评价数:</strong> ${result.spuData.reviewCount || '未提取到'}<br>
                        <strong>价格:</strong> ${result.spuData.price ? '$' + result.spuData.price : '未提取到'}<br>
                        <strong>库存状态:</strong> ${result.spuData.stockStatus || '未提取到'}<br>
                        <strong>主图:</strong> ${result.spuData.mainImageUrl ? '✅ 已提取' : '❌ 未提取到'}
                    </div>
                </div>

                <div class="result-section">
                    <h3>🔄 变体数据</h3>
                    <div class="result-item ${result.isMultiVariant ? 'success' : 'warning'}">
                        <strong>是否多变体:</strong> ${result.isMultiVariant ? '是' : '否'}<br>
                        <strong>dimensionToAsinMap:</strong> ${result.variationData.dimensionToAsinMap ? '✅ 已提取' : '❌ 未提取到'}<br>
                        <strong>colorToAsin:</strong> ${result.variationData.colorToAsin ? '✅ 已提取' : '❌ 未提取到'}<br>
                        <strong>parentAsin:</strong> ${result.variationData.parentAsin || '未提取到'}<br>
                        ${result.isMultiVariant ? `<strong>SKU ASINs:</strong> ${result.skuAsins.join(', ')}` : ''}
                    </div>
                </div>

                <div class="result-section">
                    <h3>🔍 详细数据</h3>
                    <div class="result-item">
                        <strong>变体数据JSON:</strong>
                        <div class="json-display">${JSON.stringify(result.variationData, null, 2)}</div>
                    </div>
                </div>
            `;
        }

        /**
         * 显示比较结果
         */
        function displayComparisonResults() {
            const files = Object.keys(testResults);
            if (files.length === 0) return;

            let comparisonHtml = `
                <div class="result-section">
                    <h3>📈 批量测试比较结果</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>文件大小</th>
                                <th>ASIN</th>
                                <th>标题</th>
                                <th>品牌</th>
                                <th>评分</th>
                                <th>价格</th>
                                <th>产品类型</th>
                                <th>SKU数量</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            files.forEach(filePath => {
                const result = testResults[filePath];
                comparisonHtml += `
                    <tr>
                        <td>${result.fileName}</td>
                        <td>${(result.htmlSize / 1000).toFixed(1)}K</td>
                        <td>${result.spuData.asin || '-'}</td>
                        <td>${result.spuData.title ? result.spuData.title.substring(0, 30) + '...' : '-'}</td>
                        <td>${result.spuData.brand || '-'}</td>
                        <td>${result.spuData.rating || '-'}</td>
                        <td>${result.spuData.price ? '$' + result.spuData.price : '-'}</td>
                        <td>${result.isMultiVariant ? '多变体' : '单变体'}</td>
                        <td>${result.skuAsins.length}</td>
                    </tr>
                `;
            });

            comparisonHtml += `
                        </tbody>
                    </table>
                </div>

                <div class="result-section">
                    <h3>📋 测试总结</h3>
                    <div class="result-item success">
                        <strong>测试文件数:</strong> ${files.length}<br>
                        <strong>成功解析ASIN:</strong> ${files.filter(f => testResults[f].spuData.asin !== 'UNKNOWN').length}/${files.length}<br>
                        <strong>成功解析标题:</strong> ${files.filter(f => testResults[f].spuData.title).length}/${files.length}<br>
                        <strong>多变体产品:</strong> ${files.filter(f => testResults[f].isMultiVariant).length}/${files.length}<br>
                        <strong>总SKU数量:</strong> ${files.reduce((sum, f) => sum + testResults[f].skuAsins.length, 0)}
                    </div>
                </div>
            `;

            document.getElementById('results').innerHTML = comparisonHtml;
        }
    </script>
</body>
</html>
