<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon商品详情页面数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007185;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-button {
            background-color: #007185;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background-color: #005a6b;
        }
        .test-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result-container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: white;
            max-height: 400px;
            overflow-y: auto;
        }
        .spu-data, .sku-data {
            margin-bottom: 20px;
        }
        .data-item {
            margin: 8px 0;
            padding: 8px;
            background-color: #f9f9f9;
            border-left: 3px solid #007185;
        }
        .data-label {
            font-weight: bold;
            color: #007185;
        }
        .data-value {
            margin-left: 10px;
            word-break: break-all;
        }
        .sku-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background-color: #fff;
        }
        .sku-header {
            font-weight: bold;
            color: #007185;
            margin-bottom: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .json-viewer {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ Amazon商品详情页面数据测试工具</h1>
            <p>测试Amazon商品详情页面的SPU和SKU数据提取功能</p>
        </div>

        <div class="test-section">
            <h2>📋 测试说明</h2>
            <p><strong>使用方法：</strong></p>
            <ol>
                <li>在Amazon商品详情页面打开此测试页面</li>
                <li>点击"测试SPU数据提取"按钮测试产品基础信息提取</li>
                <li>点击"测试SKU数据提取"按钮测试变体信息提取</li>
                <li>点击"完整测试"按钮进行完整的数据提取测试</li>
            </ol>
            <p><strong>注意：</strong>请确保在Amazon商品详情页面（如：https://www.amazon.com/dp/XXXXXXXXXX）使用此工具</p>
        </div>

        <div class="test-section">
            <h2>🧪 测试控制</h2>
            <button class="test-button" onclick="testSpuExtraction()">测试SPU数据提取</button>
            <button class="test-button" onclick="testSkuExtraction()">测试SKU数据提取</button>
            <button class="test-button" onclick="testFullExtraction()">完整测试</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
            
            <div id="status" class="status info" style="display: none;">
                准备就绪，请点击测试按钮开始...
            </div>
        </div>

        <div class="test-section">
            <h2>📊 SPU数据结果</h2>
            <div id="spu-results" class="result-container">
                <p>点击"测试SPU数据提取"或"完整测试"查看结果...</p>
            </div>
        </div>

        <div class="test-section">
            <h2>📦 SKU数据结果</h2>
            <div id="sku-results" class="result-container">
                <p>点击"测试SKU数据提取"或"完整测试"查看结果...</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 原始数据查看</h2>
            <button class="test-button" onclick="showRawData()">查看原始JSON数据</button>
            <div id="raw-data" class="json-viewer" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="config.js"></script>
    <script src="amazon-parser.js"></script>
    
    <script>
        // 全局变量存储测试结果
        let lastSpuData = null;
        let lastSkuData = null;
        let lastVariationData = null;

        // 显示状态信息
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        // 检查是否在Amazon页面
        function checkAmazonPage() {
            const url = window.location.href;
            if (!url.includes('amazon.com')) {
                showStatus('❌ 请在Amazon商品详情页面使用此工具！', 'error');
                return false;
            }
            if (!url.includes('/dp/') && !url.includes('/gp/product/')) {
                showStatus('⚠️ 建议在Amazon商品详情页面（/dp/XXXXXXXXXX）使用此工具', 'error');
                return false;
            }
            return true;
        }

        // 测试SPU数据提取
        async function testSpuExtraction() {
            if (!checkAmazonPage()) return;
            
            showStatus('🔄 正在提取SPU数据...', 'info');
            
            try {
                // 检查AmazonParser是否可用
                if (typeof AmazonParser === 'undefined') {
                    throw new Error('AmazonParser未加载，请确保amazon-parser.js文件存在');
                }

                // 创建解析器实例
                const parser = new AmazonParser();
                
                // 提取SPU数据
                const spuData = parser.extractSpuData(document, window.location.href);
                lastSpuData = spuData;
                
                // 显示结果
                displaySpuData(spuData);
                showStatus('✅ SPU数据提取完成！', 'success');
                
            } catch (error) {
                console.error('SPU数据提取失败:', error);
                showStatus(`❌ SPU数据提取失败: ${error.message}`, 'error');
                document.getElementById('spu-results').innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
            }
        }

        // 测试SKU数据提取
        async function testSkuExtraction() {
            if (!checkAmazonPage()) return;
            
            showStatus('🔄 正在提取SKU数据...', 'info');
            
            try {
                // 检查AmazonParser是否可用
                if (typeof AmazonParser === 'undefined') {
                    throw new Error('AmazonParser未加载，请确保amazon-parser.js文件存在');
                }

                // 创建解析器实例
                const parser = new AmazonParser();
                
                // 首先需要SPU数据来获取parentAsin
                if (!lastSpuData) {
                    const spuData = parser.extractSpuData(document, window.location.href);
                    lastSpuData = spuData;
                }
                
                // 提取变体数据
                const variationData = parser.extractVariationDataFromJs(document);
                lastVariationData = variationData;
                
                let skuDataList = [];
                
                if (variationData && variationData.dimensionToAsinMap && Object.keys(variationData.dimensionToAsinMap).length > 0) {
                    // 多变体商品
                    skuDataList = parser.extractSkuDataFromVariations(variationData, lastSpuData.asin);
                    showStatus(`✅ 检测到多变体商品，提取到 ${skuDataList.length} 个SKU`, 'success');
                } else {
                    // 单变体商品
                    skuDataList = parser.createSingleSkuFromPage(document, lastSpuData.asin);
                    showStatus('✅ 检测到单变体商品，创建了1个SKU', 'success');
                }
                
                lastSkuData = skuDataList;
                
                // 显示结果
                displaySkuData(skuDataList);
                
            } catch (error) {
                console.error('SKU数据提取失败:', error);
                showStatus(`❌ SKU数据提取失败: ${error.message}`, 'error');
                document.getElementById('sku-results').innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
            }
        }

        // 完整测试
        async function testFullExtraction() {
            if (!checkAmazonPage()) return;
            
            showStatus('🔄 开始完整数据提取测试...', 'info');
            
            // 先测试SPU
            await testSpuExtraction();
            
            // 等待一秒后测试SKU
            setTimeout(async () => {
                await testSkuExtraction();
                showStatus('✅ 完整数据提取测试完成！', 'success');
            }, 1000);
        }

        // 显示SPU数据
        function displaySpuData(spuData) {
            const container = document.getElementById('spu-results');
            
            let html = '<div class="spu-data">';
            html += '<h3>🏷️ SPU（产品基础信息）</h3>';
            
            const fields = [
                { key: 'asin', label: 'ASIN', type: 'text' },
                { key: 'title', label: '产品标题', type: 'text' },
                { key: 'brand', label: '品牌', type: 'text' },
                { key: 'rating', label: '评分', type: 'number' },
                { key: 'reviewCount', label: '评价数量', type: 'number' },
                { key: 'mainImageUrl', label: '主图URL', type: 'url' },
                { key: 'categoryPath', label: '类目路径', type: 'text' },
                { key: 'bulletPoints', label: '五点描述', type: 'json' },
                { key: 'imageUrls', label: '图片URLs', type: 'json' },
                { key: 'productDetails', label: '产品详情', type: 'json' }
            ];
            
            fields.forEach(field => {
                const value = spuData[field.key];
                html += `<div class="data-item">`;
                html += `<span class="data-label">${field.label}:</span>`;
                
                if (field.type === 'url' && value) {
                    html += `<span class="data-value"><a href="${value}" target="_blank">${value}</a></span>`;
                } else if (field.type === 'json' && value) {
                    try {
                        const parsed = typeof value === 'string' ? JSON.parse(value) : value;
                        html += `<span class="data-value">${JSON.stringify(parsed, null, 2)}</span>`;
                    } catch (e) {
                        html += `<span class="data-value">${value}</span>`;
                    }
                } else {
                    html += `<span class="data-value">${value || '未获取到'}</span>`;
                }
                html += `</div>`;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 显示SKU数据
        function displaySkuData(skuDataList) {
            const container = document.getElementById('sku-results');
            
            if (!skuDataList || skuDataList.length === 0) {
                container.innerHTML = '<p>未找到SKU数据</p>';
                return;
            }
            
            let html = '<div class="sku-data">';
            html += `<h3>📦 SKU数据 (共${skuDataList.length}个变体)</h3>`;
            
            skuDataList.forEach((sku, index) => {
                html += `<div class="sku-item">`;
                html += `<div class="sku-header">SKU ${index + 1}: ${sku.asin}</div>`;
                
                const fields = [
                    { key: 'asin', label: 'ASIN' },
                    { key: 'parentAsin', label: '父ASIN' },
                    { key: 'price', label: '价格' },
                    { key: 'currency', label: '货币' },
                    { key: 'stockStatus', label: '库存状态' },
                    { key: 'imageUrl', label: '图片URL' },
                    { key: 'variationAttributes', label: '变体属性' }
                ];
                
                fields.forEach(field => {
                    const value = sku[field.key];
                    html += `<div class="data-item">`;
                    html += `<span class="data-label">${field.label}:</span>`;
                    
                    if (field.key === 'imageUrl' && value) {
                        html += `<span class="data-value"><a href="${value}" target="_blank">${value}</a></span>`;
                    } else if (field.key === 'variationAttributes' && value) {
                        try {
                            const parsed = typeof value === 'string' ? JSON.parse(value) : value;
                            html += `<span class="data-value">${JSON.stringify(parsed, null, 2)}</span>`;
                        } catch (e) {
                            html += `<span class="data-value">${value}</span>`;
                        }
                    } else {
                        html += `<span class="data-value">${value || '未获取到'}</span>`;
                    }
                    html += `</div>`;
                });
                
                html += `</div>`;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 显示原始数据
        function showRawData() {
            const container = document.getElementById('raw-data');
            
            if (!lastSpuData && !lastSkuData) {
                container.textContent = '请先运行测试以获取数据';
                container.style.display = 'block';
                return;
            }
            
            const rawData = {
                spu: lastSpuData,
                sku: lastSkuData,
                variation: lastVariationData,
                timestamp: new Date().toISOString(),
                url: window.location.href
            };
            
            container.textContent = JSON.stringify(rawData, null, 2);
            container.style.display = 'block';
        }

        // 清空结果
        function clearResults() {
            document.getElementById('spu-results').innerHTML = '<p>点击"测试SPU数据提取"或"完整测试"查看结果...</p>';
            document.getElementById('sku-results').innerHTML = '<p>点击"测试SKU数据提取"或"完整测试"查看结果...</p>';
            document.getElementById('raw-data').style.display = 'none';
            document.getElementById('status').style.display = 'none';
            
            lastSpuData = null;
            lastSkuData = null;
            lastVariationData = null;
            
            showStatus('✅ 结果已清空', 'success');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('📋 测试工具已准备就绪，请点击测试按钮开始', 'info');
            
            // 检查当前页面
            if (window.location.href.includes('amazon.com')) {
                if (window.location.href.includes('/dp/') || window.location.href.includes('/gp/product/')) {
                    showStatus('✅ 检测到Amazon商品详情页面，可以开始测试', 'success');
                } else {
                    showStatus('⚠️ 建议在Amazon商品详情页面使用此工具', 'error');
                }
            } else {
                showStatus('❌ 请在Amazon网站使用此工具', 'error');
            }
        });
    </script>
</body>
</html>
