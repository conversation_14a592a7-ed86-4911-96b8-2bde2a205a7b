<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon采集插件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
        }
        .log-entry.info {
            color: #007bff;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Amazon采集插件测试页面</h1>
        <p>此页面用于测试Amazon采集插件的各项功能是否正常工作。</p>
        
        <div class="test-section">
            <h3>📋 扩展状态检查</h3>
            <div id="extension-status">
                <div>Chrome扩展API: <span id="chrome-api-status" class="status">检查中...</span></div>
                <div>jQuery加载: <span id="jquery-status" class="status">检查中...</span></div>
                <div>配置文件: <span id="config-status" class="status">检查中...</span></div>
                <div>Amazon解析器: <span id="parser-status" class="status">检查中...</span></div>
                <div>调试助手: <span id="debug-status" class="status">检查中...</span></div>
            </div>
            <button onclick="checkExtensionStatus()">重新检查</button>
        </div>

        <div class="test-section">
            <h3>🔗 Background通信测试</h3>
            <div id="background-status">
                <div>通信状态: <span id="bg-comm-status" class="status">未测试</span></div>
                <div>响应内容: <span id="bg-response">-</span></div>
            </div>
            <button onclick="testBackgroundCommunication()">测试Background通信</button>
        </div>

        <div class="test-section">
            <h3>💾 本地存储测试</h3>
            <div id="storage-status">
                <div>存储访问: <span id="storage-access-status" class="status">未测试</span></div>
                <div>客户端ID: <span id="client-id">-</span></div>
                <div>当前任务: <span id="current-task">-</span></div>
            </div>
            <button onclick="testLocalStorage()">测试本地存储</button>
            <button onclick="generateNewClientId()">生成新客户端ID</button>
        </div>

        <div class="test-section">
            <h3>🎯 采集功能测试</h3>
            <div id="crawling-status">
                <div>采集状态: <span id="crawl-status" class="status">未测试</span></div>
                <div>当前页面: <span id="current-page">-</span></div>
            </div>
            <button onclick="testCrawlingFunction()">测试采集功能</button>
            <button onclick="openAmazonTestPage()">打开Amazon测试页面</button>
        </div>

        <div class="test-section">
            <h3>📊 调试工具</h3>
            <button onclick="runFullDiagnosis()">运行完整诊断</button>
            <button onclick="exportDiagnosisReport()">导出诊断报告</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>📝 实时日志</h3>
            <div id="log-area" class="log-area"></div>
        </div>
    </div>

    <script>
        // 日志记录函数
        function addLog(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 更新状态显示
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }

        // 检查扩展状态
        function checkExtensionStatus() {
            addLog('开始检查扩展状态...', 'info');

            // 检查Chrome API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                updateStatus('chrome-api-status', 'success', '✅ 可用');
                addLog('Chrome扩展API可用', 'success');
            } else {
                updateStatus('chrome-api-status', 'error', '❌ 不可用');
                addLog('Chrome扩展API不可用', 'error');
            }

            // 检查jQuery
            if (typeof $ !== 'undefined') {
                updateStatus('jquery-status', 'success', '✅ 已加载');
                addLog('jQuery已加载', 'success');
            } else {
                updateStatus('jquery-status', 'error', '❌ 未加载');
                addLog('jQuery未加载', 'error');
            }

            // 检查配置文件
            if (typeof window.AmazonCrawlerConfig !== 'undefined') {
                updateStatus('config-status', 'success', '✅ 已加载');
                addLog('配置文件已加载', 'success');
            } else {
                updateStatus('config-status', 'error', '❌ 未加载');
                addLog('配置文件未加载', 'error');
            }

            // 检查Amazon解析器
            if (typeof window.AmazonParser !== 'undefined') {
                updateStatus('parser-status', 'success', '✅ 已加载');
                addLog('Amazon解析器已加载', 'success');
            } else {
                updateStatus('parser-status', 'error', '❌ 未加载');
                addLog('Amazon解析器未加载', 'error');
            }

            // 检查调试助手
            if (typeof window.debugAmazonCrawler !== 'undefined') {
                updateStatus('debug-status', 'success', '✅ 已加载');
                addLog('调试助手已加载', 'success');
            } else {
                updateStatus('debug-status', 'warning', '⚠️ 未加载');
                addLog('调试助手未加载（仅在Amazon页面可用）', 'warning');
            }
        }

        // 测试Background通信
        function testBackgroundCommunication() {
            addLog('测试Background通信...', 'info');

            if (typeof chrome === 'undefined' || !chrome.runtime) {
                updateStatus('bg-comm-status', 'error', '❌ Chrome API不可用');
                addLog('Chrome API不可用，无法测试通信', 'error');
                return;
            }

            chrome.runtime.sendMessage({
                action: "can_zidong_caiji",
                info: "test_communication"
            }, (response) => {
                if (chrome.runtime.lastError) {
                    updateStatus('bg-comm-status', 'error', '❌ 通信失败');
                    document.getElementById('bg-response').textContent = chrome.runtime.lastError.message;
                    addLog(`Background通信失败: ${chrome.runtime.lastError.message}`, 'error');
                } else {
                    updateStatus('bg-comm-status', 'success', '✅ 通信成功');
                    document.getElementById('bg-response').textContent = JSON.stringify(response);
                    addLog('Background通信成功', 'success');
                    addLog(`响应: ${JSON.stringify(response)}`, 'info');
                }
            });
        }

        // 测试本地存储
        function testLocalStorage() {
            addLog('测试本地存储...', 'info');

            if (typeof chrome === 'undefined' || !chrome.storage) {
                updateStatus('storage-access-status', 'error', '❌ Storage API不可用');
                addLog('Chrome Storage API不可用', 'error');
                return;
            }

            chrome.storage.local.get(['uniqueId', 'listTask', 'auto_start_detail_crawling'], (result) => {
                if (chrome.runtime.lastError) {
                    updateStatus('storage-access-status', 'error', '❌ 访问失败');
                    addLog(`本地存储访问失败: ${chrome.runtime.lastError.message}`, 'error');
                } else {
                    updateStatus('storage-access-status', 'success', '✅ 访问成功');
                    addLog('本地存储访问成功', 'success');

                    document.getElementById('client-id').textContent = result.uniqueId || '未设置';
                    document.getElementById('current-task').textContent = result.listTask ? 
                        JSON.stringify(result.listTask).substring(0, 50) + '...' : '无任务';

                    addLog(`客户端ID: ${result.uniqueId || '未设置'}`, 'info');
                    addLog(`自动启动详情采集: ${result.auto_start_detail_crawling || false}`, 'info');
                }
            });
        }

        // 生成新客户端ID
        function generateNewClientId() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(2, 15);
            const newId = `client_${timestamp}_${random}_test`;

            chrome.storage.local.set({ uniqueId: newId }, () => {
                if (chrome.runtime.lastError) {
                    addLog(`生成客户端ID失败: ${chrome.runtime.lastError.message}`, 'error');
                } else {
                    document.getElementById('client-id').textContent = newId;
                    addLog(`新客户端ID已生成: ${newId}`, 'success');
                }
            });
        }

        // 测试采集功能
        function testCrawlingFunction() {
            addLog('测试采集功能...', 'info');
            document.getElementById('current-page').textContent = window.location.href;

            if (typeof amazon_list_caiji === 'function') {
                updateStatus('crawl-status', 'success', '✅ 函数可用');
                addLog('amazon_list_caiji函数可用', 'success');
            } else {
                updateStatus('crawl-status', 'warning', '⚠️ 函数不可用');
                addLog('amazon_list_caiji函数不可用（仅在Amazon页面可用）', 'warning');
            }
        }

        // 打开Amazon测试页面
        function openAmazonTestPage() {
            const testUrl = 'https://www.amazon.com/s?k=laptop';
            window.open(testUrl, '_blank');
            addLog(`打开Amazon测试页面: ${testUrl}`, 'info');
        }

        // 运行完整诊断
        function runFullDiagnosis() {
            addLog('运行完整诊断...', 'info');
            
            if (typeof window.debugAmazonCrawler !== 'undefined') {
                window.debugAmazonCrawler.diagnose().then(() => {
                    addLog('完整诊断已完成', 'success');
                });
            } else {
                addLog('调试助手不可用，运行基础检查...', 'warning');
                checkExtensionStatus();
                testBackgroundCommunication();
                testLocalStorage();
                testCrawlingFunction();
            }
        }

        // 导出诊断报告
        function exportDiagnosisReport() {
            if (typeof window.debugAmazonCrawler !== 'undefined') {
                window.debugAmazonCrawler.exportReport();
                addLog('诊断报告已导出', 'success');
            } else {
                addLog('调试助手不可用，无法导出报告', 'warning');
            }
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('log-area').innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            addLog('测试页面已加载', 'info');
            setTimeout(checkExtensionStatus, 1000);
        });
    </script>
</body>
</html>
