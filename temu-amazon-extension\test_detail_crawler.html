<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon产品详情采集测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Amazon产品详情采集测试工具</h1>
        <p>此工具用于测试Amazon产品详情采集功能，包括SPU和SKU数据提取。</p>

        <!-- 测试单个产品 -->
        <div class="test-section">
            <h3>📦 测试单个产品采集</h3>
            <input type="text" class="url-input" id="productUrl" 
                   placeholder="输入Amazon产品详情页URL，例如：https://www.amazon.com/dp/B0DPPT7HYV"
                   value="https://www.amazon.com/dp/B0DPPT7HYV">
            <br>
            <button onclick="testSingleProduct()">开始采集</button>
            <button onclick="clearResults('singleResult')">清空结果</button>
            <div class="status" id="singleStatus"></div>
            <div class="result" id="singleResult"></div>
        </div>

        <!-- 启动自动采集循环 -->
        <div class="test-section">
            <h3>🔄 启动自动采集循环</h3>
            <p>从后台API获取待处理的产品详情任务，自动进行采集。</p>
            <button onclick="startDetailCrawling()">启动自动采集</button>
            <button onclick="stopDetailCrawling()">停止采集</button>
            <div class="status" id="loopStatus"></div>
            <div class="result" id="loopResult"></div>
        </div>

        <!-- 测试数据提取功能 -->
        <div class="test-section">
            <h3>🔍 测试数据提取功能</h3>
            <p>测试从当前页面提取SPU和变体数据。</p>
            <button onclick="testDataExtraction()">提取当前页面数据</button>
            <button onclick="clearResults('extractResult')">清空结果</button>
            <div class="status" id="extractStatus"></div>
            <div class="result" id="extractResult"></div>
        </div>

        <!-- 系统状态 -->
        <div class="test-section">
            <h3>📊 系统状态</h3>
            <button onclick="checkSystemStatus()">检查系统状态</button>
            <div class="status" id="systemStatus"></div>
            <div class="result" id="systemResult"></div>
        </div>
    </div>

    <script>
        let crawlingActive = false;

        // 测试单个产品采集
        async function testSingleProduct() {
            const url = document.getElementById('productUrl').value.trim();
            const statusEl = document.getElementById('singleStatus');
            const resultEl = document.getElementById('singleResult');

            if (!url) {
                showStatus(statusEl, '请输入产品URL', 'error');
                return;
            }

            if (!url.includes('amazon.com/dp/')) {
                showStatus(statusEl, '请输入有效的Amazon产品详情页URL', 'error');
                return;
            }

            showStatus(statusEl, '正在采集产品详情...', 'info');
            resultEl.textContent = '';

            try {
                // 发送消息到background script
                const response = await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        action: 'process_single_product',
                        productUrl: url,
                        taskId: Date.now()
                    }, resolve);
                });

                if (response && response.success) {
                    showStatus(statusEl, '✅ 产品采集成功！', 'success');
                    resultEl.textContent = `采集完成时间: ${new Date().toLocaleString()}\n产品URL: ${url}\n状态: 成功`;
                } else {
                    showStatus(statusEl, '❌ 产品采集失败', 'error');
                    resultEl.textContent = `错误信息: ${response?.error || '未知错误'}`;
                }
            } catch (error) {
                showStatus(statusEl, '❌ 采集过程中出错', 'error');
                resultEl.textContent = `错误详情: ${error.message}`;
            }
        }

        // 启动自动采集循环
        async function startDetailCrawling() {
            const statusEl = document.getElementById('loopStatus');
            const resultEl = document.getElementById('loopResult');

            if (crawlingActive) {
                showStatus(statusEl, '采集循环已在运行中', 'info');
                return;
            }

            showStatus(statusEl, '正在启动自动采集循环...', 'info');

            try {
                const response = await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        action: 'start_detail_crawling'
                    }, resolve);
                });

                if (response && response.success) {
                    crawlingActive = true;
                    showStatus(statusEl, '🔄 自动采集循环已启动', 'success');
                    resultEl.textContent = `启动时间: ${new Date().toLocaleString()}\n状态: 运行中\n消息: ${response.message}`;
                } else {
                    showStatus(statusEl, '❌ 启动采集循环失败', 'error');
                    resultEl.textContent = `错误信息: ${response?.error || '未知错误'}`;
                }
            } catch (error) {
                showStatus(statusEl, '❌ 启动过程中出错', 'error');
                resultEl.textContent = `错误详情: ${error.message}`;
            }
        }

        // 停止自动采集
        function stopDetailCrawling() {
            const statusEl = document.getElementById('loopStatus');
            const resultEl = document.getElementById('loopResult');

            crawlingActive = false;
            showStatus(statusEl, '⏹️ 采集循环已停止', 'info');
            resultEl.textContent += `\n停止时间: ${new Date().toLocaleString()}`;
        }

        // 测试数据提取
        async function testDataExtraction() {
            const statusEl = document.getElementById('extractStatus');
            const resultEl = document.getElementById('extractResult');

            showStatus(statusEl, '正在提取页面数据...', 'info');

            try {
                // 检查当前页面是否为Amazon产品页
                if (!window.location.href.includes('amazon.com')) {
                    showStatus(statusEl, '⚠️ 当前页面不是Amazon页面', 'error');
                    resultEl.textContent = '请在Amazon产品详情页面运行此测试';
                    return;
                }

                // 模拟数据提取
                const pageData = {
                    url: window.location.href,
                    title: document.title,
                    hasProductTitle: !!document.querySelector('#productTitle'),
                    hasVariationData: document.documentElement.outerHTML.includes('dimensionToAsinMap'),
                    hasPrice: !!document.querySelector('.a-price'),
                    hasImages: !!document.querySelector('#landingImage, #imgBlkFront')
                };

                showStatus(statusEl, '✅ 数据提取完成', 'success');
                resultEl.textContent = JSON.stringify(pageData, null, 2);

            } catch (error) {
                showStatus(statusEl, '❌ 数据提取失败', 'error');
                resultEl.textContent = `错误详情: ${error.message}`;
            }
        }

        // 检查系统状态
        function checkSystemStatus() {
            const statusEl = document.getElementById('systemStatus');
            const resultEl = document.getElementById('systemResult');

            const systemInfo = {
                extensionId: chrome.runtime.id,
                currentUrl: window.location.href,
                isAmazonPage: window.location.href.includes('amazon.com'),
                timestamp: new Date().toISOString(),
                crawlingActive: crawlingActive,
                userAgent: navigator.userAgent.substring(0, 100) + '...'
            };

            showStatus(statusEl, '✅ 系统状态正常', 'success');
            resultEl.textContent = JSON.stringify(systemInfo, null, 2);
        }

        // 显示状态信息
        function showStatus(element, message, type) {
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 清空结果
        function clearResults(elementId) {
            document.getElementById(elementId).textContent = '';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Amazon产品详情采集测试工具已加载');
            
            // 检查是否在Amazon页面
            if (window.location.href.includes('amazon.com')) {
                document.getElementById('systemStatus').textContent = '✅ 检测到Amazon页面';
                document.getElementById('systemStatus').className = 'status success';
            } else {
                document.getElementById('systemStatus').textContent = '⚠️ 当前不在Amazon页面';
                document.getElementById('systemStatus').className = 'status info';
            }
        });
    </script>
</body>
</html>
